﻿
📍 Updated SizeWise Suite – Practical Development Roadmap

(With Smart Enhancements and Team-Friendly Planning)

🧱 Phase 0.0 – Core Foundation

Objective: Build the project’s skeleton: routing, theming, validation, config, and testing setup.

✅ Product Deliverables:
• Flask backend initialized
• Routing via routes.py
• Tool registry (toolConfig.json)
• JSON-driven rule engine + schema validation
• Dark/light theme toggler
• Shared layout shell (header, nav, tool loader)
• Basic error handling & logging
• Unit testing setup (Jest or Mocha)

🔧 Implementation Plan:
Task
Folder/File
Flask App Init
/app/__init__.py, /core/routes.py
Tool Registry Logic
/core/registration/toolRegistrar.js
Schema Validator
/core/schema/schemaValidator.js
Theme Toggle
/hooks/themeToggler.js
Home UI Shell
/templates/base.html, home.html
Configuration & Defaults
/config/toolConfig.json, defaultSettings.json
Error Handling & Logging
/core/errorHandler.js, /core/logger.js
Unit Test Bootstrap
/tests/setup.js, /tests/core/

🌬️ Phase 0.1 – AirDuctSizer (formerly DuctSizer)

Objective: Launch first tool for sizing conditioned air ducts.

✅ Product Deliverables:
• Input: CFM, duct shape/size, length
• Output: velocity, pressure loss, gauge, joint, hanger spacing
• SMACNA-based validation (Tables 1-3, 2-1, etc.)
• Educational callouts (when Educated Mode ON)
• Snap summary output string
• Consistent file layout (logic, ui, events, rules, validators)

🔧 Implementation Plan:
Task
Folder/File
Tool Folder
/tools/air-duct-sizer/
Logic/UI/Events
logic.js, ui.js, events.js
Validators (shared use)
/core/validators/gauge.js, joint.js, hanger.js
Rule Files (SMACNA)
/tools/air-duct-sizer/rules/
Template
/templates/air-duct-sizer.html
Tool Settings (if needed)
/tools/air-duct-sizer/toolConfig.json
Unit Tests
/tools/air-duct-sizer/tests/

🔥 Phase 0.2 – GreaseDuctSizer (formerly GreaseSizer)

Objective: Handle grease-laden exhaust duct sizing (NFPA 96 + UL).

✅ Product Deliverables:
• Input: airflow, size, material (SS304/SS316)
• Output: clearance, pressure drop, code warnings
• Compliance validator (UL 1978/2221 + NFPA 96)
• Snap summary string
• UI with tool-specific theme (green)

🔧 Implementation Plan:
Task
Folder/File
Tool Folder
/tools/grease-duct-sizer/
Logic & UI
logic.js, ui.js, events.js
Clearance Validator
/core/validators/ulCompliance.js
Rule Files
/rules/nfpa96.json, ul1978.json
Template
/templates/grease-duct-sizer.html
Tests
/tools/grease-duct-sizer/tests/

♨️ Phase 0.3 – BoilerVentSizer

Objective: Support boiler venting (Cat I–IV, PVC/CPVC/SS, etc.)

✅ Product Deliverables:
• Input: BTU/hr, length, elbows, vent type
• Output: pressure drop, material limit warning
• Validator based on NFPA 54 + manufacturer specs

🔧 Implementation Plan:
Task
Folder/File
Tool Folder
/tools/boiler-vent-sizer/
Rule Files
/rules/nfpa54.json, /rules/boilerSpecs.json
Validator Logic
/validators/pressureCheck.js, etc.

🚛 Phase 0.4 – EngineExhaustSizer

Objective: Engine generator exhaust sizing (backpressure, velocity, etc.)

✅ Product Deliverables:
• Inputs: HP, length, slope, elbows
• Output: backpressure, exhaust velocity
• Material selection + validation (e.g. flex vs rigid)
• Snap output summary

🔁 Phase 1.x – Enhancements & Utilities

📦 Phase 1.1 – Export & Report Mode

Priority: Early user-facing feature

✅ Features:
• Export PDF + CSV with system summary
• Download ZIP: layout image, config, compliance note
• Shareable project summaries

🔧 Implementation:
Task
Folder/File
Export Engine
/services/exportService.js
PDF/CSV Logic
/core/export/pdfGenerator.js, csvBuilder.js

🧪 Phase 1.2 – Testing & Validator Coverage

Objective: Bulletproof all validators, schemas, and logic

✅ Tasks:
• Add validation schemas for every tool
• Expand unit tests (tools, shared logic)
• Test invalid inputs and edge cases

🧰 Phase 1.3 – Admin Dashboard

Objective: Lightweight internal panel for non-developers

✅ Features:
• Rule editor (basic JSON form)
• Tool toggle switches (freemium control)
• View/edit validator coverage

🎯 Phase 2.x – Simulation Mode

🧱 Phase 2.0 – AirDuct Simulation Canvas

Proof of Concept First

✅ Features:
• Canvas duct path drawing
• Airflow pressure animation
• Duct segment color-coding by velocity

🔧 Tools:
• Explore D3.js or Three.js (or simple SVG first)
• Place under /simulations/air-duct/

🔥 Phase 2.1 – Grease Simulation Add-On
• Same as above but for grease ducts
• Includes trap detection + clearance visuals

🌍 Phase 3.x – Platform Expansion

🌐 Phase 3.0 – Multi-Language Support
• Locale files: /locales/en.json, ph.json, es.json
• Auto-switch + UI toggler

⚙️ Phase 3.1 – Plugin Engine (Simplified)
• Basic system to let developers drop new tools
• Plugin schema + auto-loader

💾 Phase 3.2 – Cloud Sync (Delay if low demand)
• Add auth system
• Save/load history

🛠️ Maintenance & DevOps Phase (ongoing)
Task
Why?
CI/CD via GitHub Actions
Auto-run tests + build
CHANGELOG.md
Track releases
CONTRIBUTING.md
Welcome collaborators
Fallback handling for broken inputs
Prevent app crashes
Post-release refactoring
Reduce tech debt
User feedback via issue collector
Prioritize next features








[Type here]0[Type here]04





