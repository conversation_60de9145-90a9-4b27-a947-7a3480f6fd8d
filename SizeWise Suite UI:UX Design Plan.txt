﻿
SizeWise Suite – UI/UX Design Plan

1. 
Core Design Principles
• Professional Engineering Aesthetic: Clean, technical, and confidence-inspiring; similar to tools like Notion, Figma, or Autodesk but focused on clarity, function, and standards compliance.
• Consistency Across All Tools: Unified header bar and layout across the suite, but each tool (DuctSizer, GreaseSizer, etc.) can have its own color theme or accent for fast recognition.
• Real-Time Feedback: All calculations and validation results update instantly as the user inputs data.
• Offline-First: UI/UX works seamlessly whether connected or not. No “loading” spinners except for user-initiated sync/export.
• Accessibility: Color contrast, keyboard navigation, and font sizing must be compliant with WCAG 2.1 AA as minimum.

2. 
Top-Level Navigation & Layout
• Header Bar (Fixed on All Screens):
o Left: Home (always visible)
o Center (optional): Current tool name (e.g., DuctSizer)
o Right: Light/Dark Mode toggle, Settings (gear icon)
• Sidebar Navigation (for desktop/tablet):
o Collapsible sidebar with quick links to all tools and modules
o Visible status icons for offline/online state
• Mobile Responsiveness:
o Header collapses into a hamburger menu
o Key actions accessible with thumb reach (bottom nav or floating buttons)

3. 
Per-Tool Workspace
• Main Calculation Panel:
o Left: Input form (sectioned with clear field groupings, e.g., “Dimensions,” “Airflow,” “Material,” etc.)
o Right: Live Results Panel (updates in real time; shows outputs, warnings, and compliance flags)
o Tooltips and references pop out for each field (SMACNA/NFPA rules accessible via small (i) icons)
• Simulation Mode (Phase 2+):
o Interactive canvas in main area, with drawing controls on left and simulation outputs (airflow lines, temp coloring, node results) on right
o Live feedback as user draws/modifies ductwork

4. 
Settings & Unit Controls
• Settings Panel (Modal or Drawer):
o Real-time unit switcher (SI/Imperial)—switch updates all fields/results instantly
o Theme selection, offline data management, export/import, and diagnostics
• User Account/Profile (future):
o Sync/export controls, usage history, subscription status

5. 
Inline Validation & Compliance
• Field-level validation:
o Inline cues (e.g., red border for errors, orange for out-of-standard values)
o Hover tooltips with guidance (from SMACNA/NFPA references)
• Live Compliance Panel:
o Visual checklist of standards compliance (e.g., “Duct gauge OK per SMACNA Table 5-1,” “Pressure drop exceeds recommended value!”)
o Clickable warnings link directly to correction actions

6. 
Documentation & Help
• Quick Help:
o Each screen/tool has a floating “?” or “Guide” button opening relevant docs from /docs/guides/ or /docs/tools/
• Pop-up SMACNA/NFPA reference:
o Legal/copyright-compliant, only showing small excerpts or links to full docs as allowed

7. 
Modern Visuals & Feedback
• Minimalist Cards, Soft Shadows, Rounded Corners (2xl), Generous Padding:
o Clear card-based layout for inputs, outputs, and settings
• Subtle Animation:
o Framer Motion for panel transitions and simulation visuals (airflow, temperature gradients)
• Status Badges & Progress Bars:
o For calculations, sync, export, and validation pass/fail

8. 
Accessibility & Usability
• Font scaling for engineering readability (default: Inter, Roboto, or system font for /assets/fonts/)
• High-contrast dark and light themes (fully switchable)
• Tab/Enter keyboard flow optimized for speed entry (estimators can tab through forms rapidly)

UI/UX Scaffolding Preview (CopyInsert Format)
/app/
/   └── ui/
        ├── components/
        │   ├── HeaderBar.js
        │   ├── SidebarNav.js
        │   ├── ToolPanel.js
        │   ├── ResultsPanel.js
        │   ├── SimulationCanvas.js
        │   ├── SettingsModal.js
        │   ├── ValidationTooltip.js
        │   └── ComplianceChecklist.js
        ├── themes/
        │   ├── light.js
        │   └── dark.js
        └── styles/
            ├── base.css
            └── tool-colors.css
• HeaderBar.js: Fixed navigation, Home, Light/Dark, Settings
• SidebarNav.js: Per-tool quick nav, status, online/offline
• ToolPanel.js: Dynamic input fields, grouped by section
• ResultsPanel.js: Live outputs, warnings, compliance
• SimulationCanvas.js: Interactive drawing, real-time feedback
• SettingsModal.js: Unit switch, theme, offline data, export/import
• ValidationTooltip.js: SMACNA/NFPA rule popups
• ComplianceChecklist.js: Visual pass/fail of all standards rules

Implementation Rationale
• Why this layout?
o Field engineers need to move fast: all tools accessible, inputs and results in one view, no hunting for data.
o Modular components make it easy to add new tools or change UI per feedback.
o Visual status and warnings mean fewer errors in the field, less rework.
• Who benefits?
o HVAC estimators, field engineers, project managers, and QA—all can use and trust the interface.
• Testing Requirements:
o All UI components must pass accessibility tests, visual regression snapshots, and cross-browser/device QA.
• Versioning:
o Major UI changes logged per semantic versioning; rollbackable via Git.
• Dependencies:
o Tailwind CSS, Framer Motion, React/Vue/Vanilla JS (project choice), Mocha/Jest for tests.

Next Steps
• Wireframe creation for all tool screens
• UI component prototyping (Header, Inputs, Results, Simulation)
• Design tokens and themes for consistency
• Feedback loop with field users for iterative UI improvement

