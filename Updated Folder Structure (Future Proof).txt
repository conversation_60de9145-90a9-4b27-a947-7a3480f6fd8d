﻿/app/
│
├── assets/
│   ├── fonts/           # Only .woff2, .ttf files – All font assets; must be referenced by UI
│   ├── icons/           # Only .svg, .ico – Used in UI components; must match brand/UI style
│   ├── illustrations/   # Only .svg, .png – Diagrams, engineering illustrations, UI graphics
│   └── videos/          # Only .mp4 – Tutorial or demonstration videos, short and relevant
│
├── config/
│   └── environment/     # Only .json – Environment config, one per stage (dev, staging, prod)
│       ├── development.json
│       ├── staging.json
│       └── production.json
│
├── core/
│   ├── calculations/    # .js only – Modular core calculation logic (e.g., airflow, pressure-drop)
│   ├── validators/      # .js only – All standards-based validation rules (SMACNA, NFPA, UL)
│   ├── schemas/         # .json only – AJV/Zod-validated schemas for config, rules, input/output
│   ├── registrars/      # .js only – Tool/plugin auto-registration logic (registrar, plugin manager)
│   └── i18n/            # i18n engine, config, loaders (.js, .json for translations)
│
├── data/
│   ├── seeds/           # .json only – Initial seed data for setup/demo
│   ├── backups/         # .json only – Scheduled/manual data backups for disaster recovery
│   └── examples/        # .json only – Example data for user onboarding, testing
│
├── docs/
│   ├── architecture/    # .md, .png – System architecture diagrams, rationale, DDD mapping
│   ├── api/             # .md, .json – API docs, OpenAPI/Swagger specs, usage examples
│   ├── guides/          # .md only – User and engineering guides, step-by-step usage
│   ├── i18n/            # .md, .json – i18n strategy, translation file samples, locale policy
│   └── tools/           # .md – Per-tool documentation (DuctSizer, GreaseSizer, etc.)
│
├── hooks/
│   ├── useFormState.js  # JS – Handles UI state for forms
│   ├── useOfflineSync.js# JS – Manages offline data sync
│   ├── useTheme.js      # JS – Light/dark mode, theme toggling
│   └── useI18n.js       # JS – Locale switching, translation integration
│
├── i18n/
│   ├── config/
│   │   ├── i18n.js                # i18n engine config and loader
│   │   └── supported-locales.js   # List of supported locales
│   ├── locales/
│   │   ├── en/
│   │   │   ├── common.json
│   │   │   ├── errors.json
│   │   │   └── tools/
│   │   │       ├── duct-sizer.json
│   │   │       └── grease-sizer.json
│   │   ├── es/
│   │   │   ├── common.json
│   │   │   ├── errors.json
│   │   │   └── tools/
│   │   │       ├── duct-sizer.json
│   │   │       └── grease-sizer.json
│   │   └── ph/
│   │       ├── common.json
│   │       └── errors.json
│   └── utils/
│       ├── formatters.js          # JS – Translation and formatting helpers
│       └── pluralization.js       # JS – Pluralization logic for supported languages
│
├── plugins/
│   ├── autocad/
│   │   ├── autocad-api.js        # AutoCAD API bridge logic
│   │   └── dxf-export.js         # DXF export logic for drawings
│   └── analytics/
│       └── analytics-tracker.js  # User analytics and telemetry
│
├── services/
│   ├── api/
│   │   ├── http-client.js        # API fetch wrapper
│   │   └── websocket.js          # Realtime comms logic
│   ├── storage/
│   │   ├── localStorage.js       # Local browser storage logic
│   │   └── indexedDB.js          # IndexedDB offline storage
│   └── export/
│       ├── pdf-generator.js      # Report generation logic
│       └── csv-export.js         # CSV export logic
│
├── simulations/
│   ├── canvas/
│   │   ├── render-engine.js      # Canvas drawing/animation logic
│   │   └── layers/               # Per-layer drawing logic
│   └── physics/
│       ├── airflow.js            # Airflow simulation physics
│       └── particle-system.js    # Particle visualization for flow
│
├── static/
│   ├── css/
│   │   ├── themes/               # Per-theme stylesheets
│   │   └── components/           # Component-specific CSS
│   ├── js/
│   │   ├── lib/                  # Third-party JS libraries (MIT/OSS only)
│   │   └── utils/                # Utility functions
│   ├── images/
│   │   ├── logos/
│   │   └── ui-elements/
│   └── locales/
│       ├── en/
│       ├── es/
│       └── ph/
│
├── templates/
│   ├── components/
│   │   ├── header.html           # Header bar template
│   │   └── tool-card.html        # Per-tool card preview
│   ├── tools/
│   │   ├── duct-sizer/
│   │   │   ├── input-panel.html
│   │   │   └── results-panel.html
│   │   ├── grease-sizer/
│   │   │   ├── input-panel.html
│   │   │   └── results-panel.html
│   │   └── boiler-sizer/
│   │       ├── input-panel.html
│   │       └── results-panel.html
│   ├── base.html
│   └── error.html
│
├── tests/
│   ├── unit/
│   │   ├── core/
│   │   └── utils/
│   ├── integration/
│   ├── e2e/
│   └── fixtures/
│
└── tools/
    ├── duct-sizer/
    │   ├── assets/
    │   ├── calculations/
    │   │   ├── rectangular.js
    │   │   └── circular.js
    │   ├── components/
    │   │   ├── InputPanel.js
    │   │   └── ResultsTable.js
    │   ├── config/
    │   │   ├── defaults.json
    │   │   └── rules.json
    │   ├── i18n/
    │   │   ├── en.json
    │   │   └── es.json
    │   ├── tests/
    │   │   ├── unit/
    │   │   └── integration/
    │   ├── index.js
    │   └── README.md
    │
    ├── grease-sizer/
    │   ├── assets/
    │   ├── calculations/
    │   ├── components/
    │   ├── config/
    │   ├── i18n/
    │   │   ├── en.json
    │   │   └── es.json
    │   ├── tests/
    │   ├── index.js
    │   └── README.md
    │
    └── boiler-sizer/
        ├── assets/
        ├── calculations/
        ├── components/
        ├── config/
        ├── i18n/
        │   ├── en.json
        │   └── es.json
        ├── tests/
        ├── index.js
        └── README.md
