# SizeWise Suite Documentation

Welcome to the comprehensive documentation for SizeWise Suite - Professional HVAC Engineering Tools.

## 📖 Documentation Structure

### For Users
- **[Main README](../../README.md)** - Project overview and quick start
- **[User Guide](./guides/USER_GUIDE.md)** - How to use the tools *(Coming Soon)*
- **[Tool Tutorials](./guides/tutorials/)** - Step-by-step tutorials *(Coming Soon)*

### For Developers
- **[Getting Started](./development/GETTING_STARTED.md)** - Development setup and workflow
- **[Contributing Guide](../../CONTRIBUTING.md)** - How to contribute to the project
- **[Architecture Decisions](./architecture/)** - Technical decisions and rationale
- **[API Documentation](./api/)** - Component and calculation APIs

### For Engineers
- **[Tool Documentation](./tools/)** - Individual tool specifications and calculations
- **[Standards Compliance](./standards/)** - SMACNA, NFPA, and UL compliance *(Coming Soon)*
- **[Calculation Methods](./calculations/)** - Engineering calculation methodologies *(Coming Soon)*

### Project Management
- **[Changelog](../../CHANGELOG.md)** - Version history and changes
- **[Roadmap](./project/ROADMAP.md)** - Future development plans *(Coming Soon)*
- **[Release Notes](./project/releases/)** - Detailed release information *(Coming Soon)*

## 🏗️ Architecture Documentation

### Current ADRs (Architecture Decision Records)
1. **[ADR 001: Modern Frontend Stack](./architecture/001-modern-frontend-stack.md)** - Technology choices
2. **[ADR 002: Future-Proof Folder Structure](./architecture/002-folder-structure.md)** - Project organization

### Planned ADRs
- ADR 003: Testing Strategy and Tools
- ADR 004: UI Design System and Theming  
- ADR 005: Calculation Engine Architecture

## 🔧 Tool Documentation

### Implemented Tools
- **[Air Duct Sizer](./tools/air-duct-sizer.md)** - Duct sizing calculations (v0.1.0)

### Planned Tools
- **Grease Duct Sizer** - NFPA 96 compliant grease duct calculations
- **Boiler Vent Sizer** - Boiler venting requirements
- **Engine Exhaust Sizer** - Generator exhaust system sizing

## 📊 Current Status

### Phase 0.1 - Foundation ✅ Complete
- [x] Modern frontend stack (React + TypeScript + Vite)
- [x] Professional UI/UX with dark/light themes
- [x] Air Duct Sizer tool with basic calculations
- [x] Testing infrastructure
- [x] CI/CD pipeline
- [x] Comprehensive documentation structure

### Phase 0.2 - Enhanced Duct Sizer 🚧 In Progress
- [ ] SMACNA table integration
- [ ] Advanced pressure loss calculations
- [ ] Material selection database
- [ ] Export functionality

### Phase 0.3 - Additional Tools 📋 Planned
- [ ] Grease Duct Sizer
- [ ] Boiler Vent Sizer  
- [ ] Engine Exhaust Sizer

## 🎯 Documentation Standards

### Writing Guidelines
- **Clear and Concise**: Use simple, direct language
- **Professional Tone**: Appropriate for engineering professionals
- **Code Examples**: Include working code snippets
- **Standards References**: Cite SMACNA, NFPA, UL standards
- **Accessibility**: Follow documentation accessibility guidelines

### File Organization
- Use kebab-case for file names
- Include README.md in each directory
- Maintain consistent structure across sections
- Link between related documents

### Maintenance
- Update documentation with code changes
- Review during pull requests
- Keep examples current and working
- Archive outdated information appropriately

## 🔍 Finding Information

### Quick Reference
- **Setup Issues**: [Getting Started Guide](./development/GETTING_STARTED.md)
- **Code Standards**: [Contributing Guide](../../CONTRIBUTING.md)
- **Tool Usage**: [Tool Documentation](./tools/)
- **Technical Decisions**: [Architecture ADRs](./architecture/)

### Search Tips
- Use GitHub's search functionality
- Check the relevant tool documentation first
- Review ADRs for architectural questions
- Check CHANGELOG.md for recent changes

## 📝 Contributing to Documentation

### How to Help
1. **Fix Typos**: Submit PRs for corrections
2. **Add Examples**: Provide real-world usage examples
3. **Improve Clarity**: Suggest clearer explanations
4. **Add Missing Docs**: Document undocumented features

### Documentation PRs
- Follow the same process as code PRs
- Include screenshots for UI documentation
- Test all code examples
- Update related documentation links

## 📞 Getting Help

### For Users
- Check the [User Guide](./guides/USER_GUIDE.md) *(Coming Soon)*
- Review [Tool Documentation](./tools/)
- Create an issue for bugs or feature requests

### For Developers
- Read the [Getting Started Guide](./development/GETTING_STARTED.md)
- Check [Architecture Documentation](./architecture/)
- Review [Contributing Guidelines](../../CONTRIBUTING.md)
- Join discussions in GitHub Discussions

### For Engineers
- Review [Tool Documentation](./tools/) for calculation methods
- Check standards compliance documentation *(Coming Soon)*
- Verify calculations against published standards
- Report accuracy issues through GitHub issues

## 📈 Documentation Metrics

### Current Coverage
- **Architecture**: 2 ADRs documenting major decisions
- **Development**: Complete setup and contribution guides
- **Tools**: 1 tool fully documented (Air Duct Sizer)
- **API**: Structure established, content in progress

### Goals
- Document all implemented features
- Maintain up-to-date examples
- Provide comprehensive onboarding materials
- Ensure professional engineering accuracy

---

*This documentation is maintained by the SizeWise Suite development team. Last updated: 2024-05-30*
