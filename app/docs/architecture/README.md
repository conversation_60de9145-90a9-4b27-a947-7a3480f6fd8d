# Architecture Decision Records (ADRs)

This directory contains Architecture Decision Records for SizeWise Suite. ADRs document the significant architectural decisions made during the development of the project.

## Format

Each ADR follows this format:
- **Title**: Short descriptive title
- **Status**: Proposed, Accepted, Deprecated, Superseded
- **Context**: The situation that led to this decision
- **Decision**: What we decided to do
- **Consequences**: The positive and negative outcomes

## Index

| ADR | Title | Status | Date |
|-----|-------|--------|------|
| [001](./001-modern-frontend-stack.md) | Modern Frontend Stack Selection | Accepted | 2024-05-30 |
| [002](./002-folder-structure.md) | Future-Proof Folder Structure | Accepted | 2024-05-30 |
| [003](./003-testing-strategy.md) | Testing Strategy and Tools | Accepted | 2024-05-30 |
| [004](./004-ui-design-system.md) | UI Design System and Theming | Accepted | 2024-05-30 |
| [005](./005-calculation-architecture.md) | Calculation Engine Architecture | Accepted | 2024-05-30 |

## Guidelines

- Create a new ADR for any significant architectural decision
- Number ADRs sequentially (001, 002, etc.)
- Update the index table when adding new ADRs
- Keep ADRs concise but comprehensive
- Include code examples when relevant
