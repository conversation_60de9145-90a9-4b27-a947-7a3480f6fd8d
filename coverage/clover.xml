<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748638868412" clover="3.2.0">
  <project timestamp="1748638868412" name="All files">
    <metrics statements="104" coveredstatements="23" conditionals="41" coveredconditionals="4" methods="25" coveredmethods="4" elements="170" coveredelements="31" complexity="0" loc="104" ncloc="104" packages="4" files="9" classes="9"/>
    <package name="src">
      <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="App.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/App.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="main.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/main.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.layout">
      <metrics statements="27" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="Header.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Header.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
      <file name="Layout.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Layout.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
      <file name="Sidebar.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Sidebar.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages">
      <metrics statements="49" coveredstatements="9" conditionals="28" coveredconditionals="2" methods="13" coveredmethods="1"/>
      <file name="DuctSizerPage.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/pages/DuctSizerPage.tsx">
        <metrics statements="38" coveredstatements="9" conditionals="24" coveredconditionals="2" methods="10" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="31" count="3" type="stmt"/>
        <line num="33" count="3" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="3" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
      </file>
      <file name="Home.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/pages/Home.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
      </file>
      <file name="HomePage.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/pages/HomePage.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.providers">
      <metrics statements="19" coveredstatements="14" conditionals="5" coveredconditionals="2" methods="6" coveredmethods="3"/>
      <file name="ThemeProvider.tsx" path="/Users/<USER>/Documents/SizeWise_Suite/src/providers/ThemeProvider.tsx">
        <metrics statements="19" coveredstatements="14" conditionals="5" coveredconditionals="2" methods="6" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="30" count="3" type="stmt"/>
        <line num="31" count="3" type="stmt"/>
        <line num="32" count="3" type="stmt"/>
        <line num="33" count="3" type="stmt"/>
        <line num="34" count="3" type="stmt"/>
        <line num="37" count="3" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="3" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
