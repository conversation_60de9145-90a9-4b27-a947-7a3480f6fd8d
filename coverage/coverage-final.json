{"/Users/<USER>/Documents/SizeWise_Suite/src/App.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/App.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 73}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 35}}, "2": {"start": {"line": 5, "column": 2}, "end": {"line": 13, "column": null}}, "3": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": null}}}, "fnMap": {"0": {"name": "App", "decl": {"start": {"line": 4, "column": 9}, "end": {"line": 4, "column": 12}}, "loc": {"start": {"line": 4, "column": 12}, "end": {"line": 14, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Documents/SizeWise_Suite/src/main.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/main.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 39}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 23}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 27}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 10, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {}}, "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Header.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Header.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 57}}, "4": {"start": {"line": 6, "column": 25}, "end": {"line": 50, "column": 1}}, "5": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": 43}}, "6": {"start": {"line": 9, "column": 2}, "end": {"line": 49, "column": 4}}, "7": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 28}}, "loc": {"start": {"line": 6, "column": 30}, "end": {"line": 50, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 15}, "end": {"line": 34, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 32, "column": 16}, "end": {"line": 32, "column": 77}}, {"start": {"line": 34, "column": 16}, "end": {"line": 34, "column": 76}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Layout.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Layout.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "3": {"start": {"line": 9, "column": 38}, "end": {"line": 21, "column": 1}}, "4": {"start": {"line": 10, "column": 2}, "end": {"line": 20, "column": 4}}, "5": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 38}, "end": {"line": 9, "column": 39}}, "loc": {"start": {"line": 9, "column": 55}, "end": {"line": 21, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Sidebar.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/components/layout/Sidebar.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 53}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 62}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 28}}, "4": {"start": {"line": 6, "column": 19}, "end": {"line": 12, "column": 2}}, "5": {"start": {"line": 14, "column": 26}, "end": {"line": 51, "column": 1}}, "6": {"start": {"line": 15, "column": 19}, "end": {"line": 15, "column": 32}}, "7": {"start": {"line": 17, "column": 2}, "end": {"line": 50, "column": 4}}, "8": {"start": {"line": 21, "column": 27}, "end": {"line": 21, "column": 58}}, "9": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 32}}, "10": {"start": {"line": 24, "column": 10}, "end": {"line": 46, "column": 12}}, "11": {"start": {"line": 36, "column": 30}, "end": {"line": 36, "column": 65}}, "12": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 23}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 29}}, "loc": {"start": {"line": 14, "column": 31}, "end": {"line": 51, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 24}, "end": {"line": 20, "column": 25}}, "loc": {"start": {"line": 20, "column": 33}, "end": {"line": 47, "column": 9}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 24}}, "loc": {"start": {"line": 36, "column": 30}, "end": {"line": 36, "column": 65}}}}, "branchMap": {"0": {"loc": {"start": {"line": 32, "column": 95}, "end": {"line": 32, "column": 122}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 95}, "end": {"line": 32, "column": 104}}, {"start": {"line": 32, "column": 108}, "end": {"line": 32, "column": 122}}]}, "1": {"loc": {"start": {"line": 36, "column": 30}, "end": {"line": 36, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 30}, "end": {"line": 36, "column": 43}}, {"start": {"line": 36, "column": 47}, "end": {"line": 36, "column": 65}}]}, "2": {"loc": {"start": {"line": 40, "column": 15}, "end": {"line": 43, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 28}}, {"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/Documents/SizeWise_Suite/src/pages/DuctSizerPage.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/pages/DuctSizerPage.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 21, "column": 32}, "end": {"line": 262, "column": 1}}, "3": {"start": {"line": 22, "column": 30}, "end": {"line": 29, "column": 4}}, "4": {"start": {"line": 31, "column": 32}, "end": {"line": 31, "column": 66}}, "5": {"start": {"line": 33, "column": 28}, "end": {"line": 35, "column": 3}}, "6": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 53}}, "7": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 50}}, "8": {"start": {"line": 37, "column": 27}, "end": {"line": 81, "column": 3}}, "9": {"start": {"line": 38, "column": 16}, "end": {"line": 38, "column": 38}}, "10": {"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 44}}, "11": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 32}}, "12": {"start": {"line": 41, "column": 25}, "end": {"line": 41, "column": 32}}, "13": {"start": {"line": 46, "column": 4}, "end": {"line": 59, "column": 5}}, "14": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 44}}, "15": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": 46}}, "16": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 36}}, "17": {"start": {"line": 49, "column": 29}, "end": {"line": 49, "column": 36}}, "18": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 36}}, "19": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 44}}, "20": {"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 50}}, "21": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 28}}, "22": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 28}}, "23": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 59}}, "24": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 44}}, "25": {"start": {"line": 61, "column": 21}, "end": {"line": 61, "column": 31}}, "26": {"start": {"line": 62, "column": 30}, "end": {"line": 62, "column": 52}}, "27": {"start": {"line": 65, "column": 27}, "end": {"line": 65, "column": 31}}, "28": {"start": {"line": 66, "column": 25}, "end": {"line": 66, "column": 107}}, "29": {"start": {"line": 69, "column": 16}, "end": {"line": 69, "column": 20}}, "30": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 39}}, "31": {"start": {"line": 70, "column": 26}, "end": {"line": 70, "column": 39}}, "32": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 39}}, "33": {"start": {"line": 71, "column": 26}, "end": {"line": 71, "column": 39}}, "34": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 39}}, "35": {"start": {"line": 72, "column": 26}, "end": {"line": 72, "column": 39}}, "36": {"start": {"line": 74, "column": 4}, "end": {"line": 80, "column": 7}}, "37": {"start": {"line": 83, "column": 2}, "end": {"line": 261, "column": 4}}, "38": {"start": {"line": 111, "column": 33}, "end": {"line": 111, "column": 73}}, "39": {"start": {"line": 124, "column": 33}, "end": {"line": 124, "column": 105}}, "40": {"start": {"line": 142, "column": 37}, "end": {"line": 142, "column": 79}}, "41": {"start": {"line": 154, "column": 37}, "end": {"line": 154, "column": 80}}, "42": {"start": {"line": 168, "column": 35}, "end": {"line": 168, "column": 80}}, "43": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": 76}}, "44": {"start": {"line": 264, "column": 0}, "end": {"line": 264, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 32}, "end": {"line": 21, "column": 35}}, "loc": {"start": {"line": 21, "column": 37}, "end": {"line": 262, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 28}, "end": {"line": 33, "column": 29}}, "loc": {"start": {"line": 33, "column": 71}, "end": {"line": 35, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 18}}, "loc": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 50}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": 30}}, "loc": {"start": {"line": 37, "column": 32}, "end": {"line": 81, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 111, "column": 26}, "end": {"line": 111, "column": 27}}, "loc": {"start": {"line": 111, "column": 33}, "end": {"line": 111, "column": 73}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 124, "column": 26}, "end": {"line": 124, "column": 27}}, "loc": {"start": {"line": 124, "column": 33}, "end": {"line": 124, "column": 105}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 142, "column": 30}, "end": {"line": 142, "column": 31}}, "loc": {"start": {"line": 142, "column": 37}, "end": {"line": 142, "column": 79}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 154, "column": 30}, "end": {"line": 154, "column": 31}}, "loc": {"start": {"line": 154, "column": 37}, "end": {"line": 154, "column": 80}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 168, "column": 28}, "end": {"line": 168, "column": 29}}, "loc": {"start": {"line": 168, "column": 35}, "end": {"line": 168, "column": 80}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 183, "column": 26}, "end": {"line": 183, "column": 27}}, "loc": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": 76}}}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 32}}, "type": "if", "locations": [{"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 32}}]}, "1": {"loc": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 12}}, {"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 23}}]}, "2": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 46, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {"line": 53, "column": 11}, "end": {"line": 59, "column": 5}}]}, "3": {"loc": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 36}}, "type": "if", "locations": [{"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 36}}]}, "4": {"loc": {"start": {"line": 49, "column": 10}, "end": {"line": 49, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 10}, "end": {"line": 49, "column": 16}}, {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 27}}]}, "5": {"loc": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 28}}, "type": "if", "locations": [{"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 28}}]}, "6": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 39}}, "type": "if", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 39}}]}, "7": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 39}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 39}}]}, "8": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 39}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 39}}]}, "9": {"loc": {"start": {"line": 78, "column": 20}, "end": {"line": 78, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 38}, "end": {"line": 78, "column": 39}}, {"start": {"line": 78, "column": 42}, "end": {"line": 78, "column": 43}}]}, "10": {"loc": {"start": {"line": 133, "column": 13}, "end": {"line": 172, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 14}, "end": {"line": 158, "column": null}}, {"start": {"line": 161, "column": 14}, "end": {"line": 171, "column": null}}]}, "11": {"loc": {"start": {"line": 205, "column": 11}, "end": {"line": 256, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 206, "column": 12}, "end": {"line": 251, "column": null}}, {"start": {"line": 254, "column": 12}, "end": {"line": 254, "column": null}}]}, "12": {"loc": {"start": {"line": 241, "column": 19}, "end": {"line": 242, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 19}, "end": {"line": 241, "column": 42}}, {"start": {"line": 242, "column": 20}, "end": {"line": 242, "column": 96}}]}, "13": {"loc": {"start": {"line": 244, "column": 19}, "end": {"line": 245, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 244, "column": 19}, "end": {"line": 244, "column": 41}}, {"start": {"line": 245, "column": 20}, "end": {"line": 245, "column": 81}}]}, "14": {"loc": {"start": {"line": 247, "column": 19}, "end": {"line": 248, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 247, "column": 19}, "end": {"line": 247, "column": 45}}, {"start": {"line": 248, "column": 20}, "end": {"line": 248, "column": 77}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 3, "4": 3, "5": 3, "6": 0, "7": 0, "8": 3, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 3, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 1}, "f": {"0": 3, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0, 0], "10": [3, 0], "11": [0, 3], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "/Users/<USER>/Documents/SizeWise_Suite/src/pages/Home.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/pages/Home.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 16}}, "1": {"start": {"line": 2, "column": 2}, "end": {"line": 7, "column": null}}}, "fnMap": {"0": {"name": "Home", "decl": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 20}}, "loc": {"start": {"line": 1, "column": 20}, "end": {"line": 8, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Documents/SizeWise_Suite/src/pages/HomePage.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/pages/HomePage.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 26}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 68}}, "3": {"start": {"line": 5, "column": 14}, "end": {"line": 38, "column": 2}}, "4": {"start": {"line": 40, "column": 27}, "end": {"line": 128, "column": 1}}, "5": {"start": {"line": 41, "column": 2}, "end": {"line": 127, "column": 4}}, "6": {"start": {"line": 57, "column": 23}, "end": {"line": 57, "column": 32}}, "7": {"start": {"line": 58, "column": 10}, "end": {"line": 90, "column": 12}}, "8": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 40, "column": 27}, "end": {"line": 40, "column": 30}}, "loc": {"start": {"line": 40, "column": 32}, "end": {"line": 128, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 56, "column": 19}, "end": {"line": 56, "column": 20}}, "loc": {"start": {"line": 56, "column": 28}, "end": {"line": 91, "column": 9}}}}, "branchMap": {"0": {"loc": {"start": {"line": 61, "column": 37}, "end": {"line": 61, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 61, "column": 55}, "end": {"line": 61, "column": 67}}, {"start": {"line": 61, "column": 70}, "end": {"line": 61, "column": 87}}]}, "1": {"loc": {"start": {"line": 74, "column": 19}, "end": {"line": 85, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 20}, "end": {"line": 80, "column": null}}, {"start": {"line": 83, "column": 20}, "end": {"line": 83, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Documents/SizeWise_Suite/src/providers/ThemeProvider.tsx": {"path": "/Users/<USER>/Documents/SizeWise_Suite/src/providers/ThemeProvider.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 78}}, "1": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 75}}, "2": {"start": {"line": 12, "column": 24}, "end": {"line": 18, "column": 1}}, "3": {"start": {"line": 13, "column": 18}, "end": {"line": 13, "column": 42}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 68}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 17}}, "7": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 24}}, "8": {"start": {"line": 24, "column": 59}, "end": {"line": 46, "column": 1}}, "9": {"start": {"line": 25, "column": 28}, "end": {"line": 28, "column": 4}}, "10": {"start": {"line": 26, "column": 23}, "end": {"line": 26, "column": 61}}, "11": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 33}}, "12": {"start": {"line": 30, "column": 2}, "end": {"line": 35, "column": 14}}, "13": {"start": {"line": 31, "column": 17}, "end": {"line": 31, "column": 48}}, "14": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 43}}, "15": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 30}}, "16": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 41}}, "17": {"start": {"line": 37, "column": 22}, "end": {"line": 39, "column": 3}}, "18": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 72}}, "19": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 69}}, "20": {"start": {"line": 41, "column": 2}, "end": {"line": 45, "column": 4}}, "21": {"start": {"line": 24, "column": 13}, "end": {"line": 24, "column": 59}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 27}}, "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 18, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 59}, "end": {"line": 24, "column": 60}}, "loc": {"start": {"line": 24, "column": 76}, "end": {"line": 46, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 44}, "end": {"line": 25, "column": 47}}, "loc": {"start": {"line": 25, "column": 49}, "end": {"line": 28, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 30, "column": 12}, "end": {"line": 30, "column": 15}}, "loc": {"start": {"line": 30, "column": 17}, "end": {"line": 35, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 25}}, "loc": {"start": {"line": 37, "column": 27}, "end": {"line": 39, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 38, "column": 13}, "end": {"line": 38, "column": 14}}, "loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 69}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}]}, "1": {"loc": {"start": {"line": 27, "column": 11}, "end": {"line": 27, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 11}, "end": {"line": 27, "column": 21}}, {"start": {"line": 27, "column": 25}, "end": {"line": 27, "column": 32}}]}, "2": {"loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 38, "column": 53}, "end": {"line": 38, "column": 59}}, {"start": {"line": 38, "column": 62}, "end": {"line": 38, "column": 69}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1, "8": 1, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 3, "17": 3, "18": 0, "19": 0, "20": 3, "21": 1}, "f": {"0": 0, "1": 3, "2": 3, "3": 3, "4": 0, "5": 0}, "b": {"0": [0], "1": [3, 1], "2": [0, 0]}}}