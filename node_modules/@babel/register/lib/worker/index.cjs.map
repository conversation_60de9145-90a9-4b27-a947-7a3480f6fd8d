{"version": 3, "names": ["babel", "require", "handleMessage", "workerTheads", "parentPort", "addListener", "_asyncToGenerator", "signal", "port", "action", "payload", "response", "init", "result", "error", "errorData", "Object", "assign", "postMessage", "_unused", "Error", "close", "Atomics", "store", "notify"], "sources": ["../../src/worker/index.cts"], "sourcesContent": ["import type { MessagePort } from \"node:worker_threads\";\nimport type { ACTIONS } from \"../types.cts\";\n\nconst babel = require(\"./babel-core.cjs\");\nimport handleMessage = require(\"./handle-message.cjs\");\n\nimport workerTheads = require(\"worker_threads\");\n\nworkerTheads.parentPort.addListener(\n  \"message\",\n  // eslint-disable-next-line @typescript-eslint/no-misused-promises\n  async ({\n    signal,\n    port,\n    action,\n    payload,\n  }: {\n    signal: Int32Array;\n    port: MessagePort;\n    action: ACTIONS;\n    payload: any;\n  }) => {\n    let response;\n\n    try {\n      if (babel.init) await babel.init;\n\n      response = { result: await handleMessage(action, payload) };\n    } catch (error) {\n      response = { error, errorData: { ...error } };\n    }\n\n    try {\n      port.postMessage(response);\n    } catch {\n      port.postMessage({\n        error: new Error(\"Cannot serialize worker response\"),\n      });\n    } finally {\n      port.close();\n      Atomics.store(signal, 0, 1);\n      Atomics.notify(signal, 0);\n    }\n  },\n);\n"], "mappings": ";;;;AAGA,MAAMA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAAC,MACnCC,aAAa,GAAAD,OAAA,CAAW,sBAAsB;AAAA,MAE9CE,YAAY,GAAAF,OAAA,CAAW,gBAAgB;AAE9CE,YAAY,CAACC,UAAU,CAACC,WAAW,CACjC,SAAS,EAAAC,iBAAA,CAET,WAAO;EACLC,MAAM;EACNC,IAAI;EACJC,MAAM;EACNC;AAMF,CAAC,EAAK;EACJ,IAAIC,QAAQ;EAEZ,IAAI;IACF,IAAIX,KAAK,CAACY,IAAI,EAAE,MAAMZ,KAAK,CAACY,IAAI;IAEhCD,QAAQ,GAAG;MAAEE,MAAM,QAAQX,aAAa,CAACO,MAAM,EAAEC,OAAO;IAAE,CAAC;EAC7D,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdH,QAAQ,GAAG;MAAEG,KAAK;MAAEC,SAAS,EAAAC,MAAA,CAAAC,MAAA,KAAOH,KAAK;IAAG,CAAC;EAC/C;EAEA,IAAI;IACFN,IAAI,CAACU,WAAW,CAACP,QAAQ,CAAC;EAC5B,CAAC,CAAC,OAAAQ,OAAA,EAAM;IACNX,IAAI,CAACU,WAAW,CAAC;MACfJ,KAAK,EAAE,IAAIM,KAAK,CAAC,kCAAkC;IACrD,CAAC,CAAC;EACJ,CAAC,SAAS;IACRZ,IAAI,CAACa,KAAK,CAAC,CAAC;IACZC,OAAO,CAACC,KAAK,CAAChB,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3Be,OAAO,CAACE,MAAM,CAACjB,MAAM,EAAE,CAAC,CAAC;EAC3B;AACF,CAAC,CACH,CAAC", "ignoreList": []}