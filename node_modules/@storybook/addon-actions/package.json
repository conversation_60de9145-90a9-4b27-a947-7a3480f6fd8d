{"name": "@storybook/addon-actions", "version": "7.6.20", "description": "Get UI feedback when an action is performed on an interactive element", "keywords": ["storybook", "essentials", "data-state"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/actions", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/actions"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./decorator": {"types": "./dist/decorator.d.ts", "require": "./dist/decorator.js", "import": "./dist/decorator.mjs"}, "./manager": "./dist/manager.js", "./preview": "./dist/preview.js", "./register.js": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "react-native": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "decorator": ["dist/decorator.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/core-events": "7.6.20", "@storybook/global": "^5.0.0", "@types/uuid": "^9.0.1", "dequal": "^2.0.2", "polished": "^4.2.2", "uuid": "^9.0.0"}, "devDependencies": {"@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/theming": "7.6.20", "@storybook/types": "7.6.20", "react": "^16.8.0", "react-dom": "^16.8.0", "react-inspector": "^6.0.0", "telejson": "^7.2.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/decorator.ts", "./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "storybook": {"displayName": "Actions", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/263385/101991666-479cc600-3c7c-11eb-837b-be4e5ffa1bb8.png"}}