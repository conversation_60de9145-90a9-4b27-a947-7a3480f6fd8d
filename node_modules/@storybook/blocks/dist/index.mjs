import { getControlSetterButtonId, getControlId } from './chunk-GWAJ4KRU.mjs';
import { styled, ignoreSsrWarning, themes, ThemeProvider, convert, useTheme, ensure } from '@storybook/theming';
import React17, { createContext, lazy, Fragment, useState, useCallback, useRef, useEffect, Component, cloneElement, useMemo, Suspense, useContext, Children } from 'react';
import { withR<PERSON>t, SyntaxHighlighter, FlexBar, codeCommon, Icons, IconButton, Form, components, IconButtonSkeleton, Zoom as Zoom$1, ActionBar, Link, ResetWrapper, Code, nameSpaceClassNames, H3, H2, Loader, TabsState, ErrorFormatter, getStoryHref, WithTooltipPure } from '@storybook/components';
import { transparentize, darken, opacify, lighten, rgba } from 'polished';
import { global } from '@storybook/global';
import pickBy from 'lodash/pickBy.js';
import { includeConditionalArg } from '@storybook/csf';
import { deprecate, once, logger } from '@storybook/client-logger';
import Markdown from 'markdown-to-jsx';
import memoize from 'memoizerific';
import uniq from 'lodash/uniq.js';
import cloneDeep from 'lodash/cloneDeep.js';
import { filterArgTypes, composeConfigs, Preview as Preview$1, DocsContext as DocsContext$1 } from '@storybook/preview-api';
import mapValues from 'lodash/mapValues.js';
import { STORY_ARGS_UPDATED, UPDATE_STORY_ARGS, RESET_STORY_ARGS, GLOBALS_UPDATED, NAVIGATE_URL } from '@storybook/core-events';
import dedent2 from 'ts-dedent';
import { SNIPPET_RENDERED, SourceType, str } from '@storybook/docs-tools';
import { stringify } from 'telejson';
import * as tocbot from 'tocbot';
import { Channel } from '@storybook/channels';

var Wrapper=styled.div(withReset,({theme})=>({backgroundColor:theme.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:theme.appBorderRadius,border:`1px dashed ${theme.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:transparentize(.3,theme.color.defaultText),fontSize:theme.typography.size.s2})),EmptyBlock=props=>React17.createElement(Wrapper,{...props,className:"docblock-emptyblock sb-unstyled"});var StyledSyntaxHighlighter=styled(SyntaxHighlighter)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:theme.appBorderRadius,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}}));var SourceSkeletonWrapper=styled.div(({theme})=>({background:theme.background.content,borderRadius:theme.appBorderRadius,border:`1px solid ${theme.appBorderColor}`,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),SourceSkeletonPlaceholder=styled.div(({theme})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${ignoreSsrWarning}`]:{margin:0}})),SourceSkeleton=()=>React17.createElement(SourceSkeletonWrapper,null,React17.createElement(SourceSkeletonPlaceholder,null),React17.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}}),React17.createElement(SourceSkeletonPlaceholder,{style:{width:"30%"}}),React17.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}})),Source=({isLoading,error,language,code,dark,format:format2,...rest})=>{if(isLoading)return React17.createElement(SourceSkeleton,null);if(error)return React17.createElement(EmptyBlock,null,error);let syntaxHighlighter=React17.createElement(StyledSyntaxHighlighter,{bordered:!0,copyable:!0,format:format2,language,className:"docblock-source sb-unstyled",...rest},code);if(typeof dark>"u")return syntaxHighlighter;let overrideTheme=dark?themes.dark:themes.light;return React17.createElement(ThemeProvider,{theme:convert(overrideTheme)},syntaxHighlighter)};Source.defaultProps={format:!1};var toGlobalSelector=element=>`& :where(${element}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${element}))`,breakpoint=600,Title=styled.h1(withReset,({theme})=>({color:theme.color.defaultText,fontSize:theme.typography.size.m3,fontWeight:theme.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),Subtitle=styled.h2(withReset,({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.m1,lineHeight:"28px",marginBottom:24},color:transparentize(.25,theme.color.defaultText)})),DocsContent=styled.div(({theme})=>{let reset={fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},headers={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:theme.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},code={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:theme.typography.size.s2-1,border:theme.base==="light"?`1px solid ${theme.color.mediumlight}`:`1px solid ${theme.color.darker}`,color:theme.base==="light"?transparentize(.1,theme.color.defaultText):transparentize(.3,theme.color.defaultText),backgroundColor:theme.base==="light"?theme.color.lighter:theme.color.border};return {maxWidth:1e3,width:"100%",[toGlobalSelector("a")]:{...reset,fontSize:"inherit",lineHeight:"24px",color:theme.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[toGlobalSelector("blockquote")]:{...reset,margin:"16px 0",borderLeft:`4px solid ${theme.color.medium}`,padding:"0 15px",color:theme.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[toGlobalSelector("div")]:reset,[toGlobalSelector("dl")]:{...reset,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[toGlobalSelector("h1")]:{...reset,...headers,fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h2")]:{...reset,...headers,fontSize:`${theme.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${theme.appBorderColor}`},[toGlobalSelector("h3")]:{...reset,...headers,fontSize:`${theme.typography.size.m1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h4")]:{...reset,...headers,fontSize:`${theme.typography.size.s3}px`},[toGlobalSelector("h5")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`},[toGlobalSelector("h6")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark},[toGlobalSelector("hr")]:{border:"0 none",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0},[toGlobalSelector("img")]:{maxWidth:"100%"},[toGlobalSelector("li")]:{...reset,fontSize:theme.typography.size.s2,color:theme.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":code},[toGlobalSelector("ol")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[toGlobalSelector("p")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",color:theme.color.defaultText,"& code":code},[toGlobalSelector("pre")]:{...reset,fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[toGlobalSelector("span")]:{...reset,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${theme.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:theme.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[toGlobalSelector("table")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:theme.base==="dark"?theme.color.darker:theme.color.lighter},"& tr th":{fontWeight:"bold",color:theme.color.defaultText,border:`1px solid ${theme.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${theme.appBorderColor}`,color:theme.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[toGlobalSelector("ul")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),DocsWrapper=styled.div(({theme})=>({background:theme.background.content,display:"flex",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${breakpoint}px)`]:{}})),DocsPageWrapper=({children,toc})=>React17.createElement(DocsWrapper,{className:"sbdocs sbdocs-wrapper"},React17.createElement(DocsContent,{className:"sbdocs sbdocs-content"},children),toc);var getBlockBackgroundStyle=theme=>({borderRadius:theme.appBorderRadius,background:theme.background.content,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${theme.appBorderColor}`});var Zoom=({zoom,resetZoom})=>React17.createElement(React17.Fragment,null,React17.createElement(IconButton,{key:"zoomin",onClick:e=>{e.preventDefault(),zoom(.8);},title:"Zoom in"},React17.createElement(Icons,{icon:"zoom"})),React17.createElement(IconButton,{key:"zoomout",onClick:e=>{e.preventDefault(),zoom(1.25);},title:"Zoom out"},React17.createElement(Icons,{icon:"zoomout"})),React17.createElement(IconButton,{key:"zoomreset",onClick:e=>{e.preventDefault(),resetZoom();},title:"Reset zoom"},React17.createElement(Icons,{icon:"zoomreset"}))),Bar=styled(FlexBar)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),Toolbar=({isLoading,storyId,baseUrl,zoom,resetZoom,...rest})=>React17.createElement(Bar,{...rest},React17.createElement(Fragment,{key:"left"},isLoading?[1,2,3].map(key=>React17.createElement(IconButtonSkeleton,{key})):React17.createElement(Zoom,{zoom,resetZoom})));var ZoomContext=createContext({scale:1});var{window:globalWindow}=global,IFrame=class extends Component{constructor(){super(...arguments);this.iframe=null;}componentDidMount(){let{id}=this.props;this.iframe=globalWindow.document.getElementById(id);}shouldComponentUpdate(nextProps){let{scale}=nextProps;return scale!==this.props.scale&&this.setIframeBodyStyle({width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:"top left"}),!1}setIframeBodyStyle(style){return Object.assign(this.iframe.contentDocument.body.style,style)}render(){let{id,title,src,allowFullScreen,scale,...rest}=this.props;return React17.createElement("iframe",{id,title,src,...allowFullScreen?{allow:"fullscreen"}:{},loading:"lazy",...rest})}};var{PREVIEW_URL}=global,BASE_URL=PREVIEW_URL||"iframe.html",storyBlockIdFromId=({story,primary})=>`story--${story.id}${primary?"--primary":""}`,InlineStory=props=>{let storyRef=useRef(),[showLoader,setShowLoader]=useState(!0),[error,setError]=useState(),{story,height,autoplay,forceInitialArgs,renderStoryToElement}=props;return useEffect(()=>{if(!(story&&storyRef.current))return ()=>{};let element=storyRef.current,cleanup=renderStoryToElement(story,element,{showMain:()=>{},showError:({title,description})=>setError(new Error(`${title} - ${description}`)),showException:err=>setError(err)},{autoplay,forceInitialArgs});return setShowLoader(!1),()=>{Promise.resolve().then(()=>cleanup());}},[autoplay,renderStoryToElement,story]),error?React17.createElement("pre",null,React17.createElement(ErrorFormatter,{error})):React17.createElement(React17.Fragment,null,height?React17.createElement("style",null,`#${storyBlockIdFromId(props)} { min-height: ${height}; transform: translateZ(0); overflow: auto }`):null,showLoader&&React17.createElement(StorySkeleton,null),React17.createElement("div",{ref:storyRef,id:`${storyBlockIdFromId(props)}-inner`,"data-name":story.name}))},IFrameStory=({story,height="500px"})=>React17.createElement("div",{style:{width:"100%",height}},React17.createElement(ZoomContext.Consumer,null,({scale})=>React17.createElement(IFrame,{key:"iframe",id:`iframe--${story.id}`,title:story.name,src:getStoryHref(BASE_URL,story.id,{viewMode:"story"}),allowFullScreen:!0,scale,style:{width:"100%",height:"100%",border:"0 none"}}))),Story=props=>{let{inline}=props;return React17.createElement("div",{id:storyBlockIdFromId(props),className:"sb-story sb-unstyled","data-story-block":"true"},inline?React17.createElement(InlineStory,{...props}):React17.createElement(IFrameStory,{...props}))},StorySkeleton=()=>React17.createElement(Loader,null);var ChildrenContainer=styled.div(({isColumn,columns,layout})=>({display:isColumn||!columns?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:isColumn?"column":"row","& .innerZoomElementWrapper > *":isColumn?{width:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout="padded"})=>layout==="centered"||layout==="padded"?{padding:"30px 20px","& .innerZoomElementWrapper > *":{width:"auto",border:"10px solid transparent!important"}}:{},({layout="padded"})=>layout==="centered"?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns})=>columns&&columns>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${columns} - 20px)`}}:{}),StyledSource=styled(Source)(({theme})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:theme.appBorderRadius,borderBottomRightRadius:theme.appBorderRadius,border:"none",background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":darken(.05,theme.background.content),color:theme.color.lightest,button:{background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":darken(.05,theme.background.content)}})),PreviewContainer=styled.div(({theme,withSource,isExpanded})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...getBlockBackgroundStyle(theme),borderBottomLeftRadius:withSource&&isExpanded&&0,borderBottomRightRadius:withSource&&isExpanded&&0,borderBottomWidth:isExpanded&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar})=>withToolbar&&{paddingTop:40}),getSource=(withSource,expanded,setExpanded)=>{switch(!0){case!!(withSource&&withSource.error):return {source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:()=>setExpanded(!1)}};case expanded:return {source:React17.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:()=>setExpanded(!1)}};default:return {source:React17.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:()=>setExpanded(!0)}}}};function getStoryId(children){if(Children.count(children)===1){let elt=children;if(elt.props)return elt.props.id}return null}var PositionedToolbar=styled(Toolbar)({position:"absolute",top:0,left:0,right:0,height:40}),Relative=styled.div({overflow:"hidden",position:"relative"}),Preview=({isLoading,isColumn,columns,children,withSource,withToolbar=!1,isExpanded=!1,additionalActions,className,layout="padded",...props})=>{let[expanded,setExpanded]=useState(isExpanded),{source,actionItem}=getSource(withSource,expanded,setExpanded),[scale,setScale]=useState(1),previewClasses=[className].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),defaultActionItems=withSource?[actionItem]:[],[additionalActionItems,setAdditionalActionItems]=useState(additionalActions?[...additionalActions]:[]),actionItems=[...defaultActionItems,...additionalActionItems],{window:globalWindow4}=global,copyToClipboard=useCallback(async text=>{let{createCopyToClipboardFunction}=await import('@storybook/components');createCopyToClipboardFunction();},[]),onCopyCapture=e=>{let selection=globalWindow4.getSelection();selection&&selection.type==="Range"||(e.preventDefault(),additionalActionItems.filter(item=>item.title==="Copied").length===0&&copyToClipboard(source.props.code).then(()=>{setAdditionalActionItems([...additionalActionItems,{title:"Copied",onClick:()=>{}}]),globalWindow4.setTimeout(()=>setAdditionalActionItems(additionalActionItems.filter(item=>item.title!=="Copied")),1500);}));};return React17.createElement(PreviewContainer,{withSource,withToolbar,...props,className:previewClasses.join(" ")},withToolbar&&React17.createElement(PositionedToolbar,{isLoading,border:!0,zoom:z=>setScale(scale*z),resetZoom:()=>setScale(1),storyId:getStoryId(children),baseUrl:"./iframe.html"}),React17.createElement(ZoomContext.Provider,{value:{scale}},React17.createElement(Relative,{className:"docs-story",onCopyCapture:withSource&&onCopyCapture},React17.createElement(ChildrenContainer,{isColumn:isColumn||!Array.isArray(children),columns,layout},React17.createElement(Zoom$1.Element,{scale},Array.isArray(children)?children.map((child,i)=>React17.createElement("div",{key:i},child)):React17.createElement("div",null,children))),React17.createElement(ActionBar,{actionItems}))),withSource&&expanded&&source)},StyledPreview=styled(Preview)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}})),PreviewSkeleton=()=>React17.createElement(StyledPreview,{isLoading:!0,withToolbar:!0},React17.createElement(StorySkeleton,null));var Table=styled.table(({theme})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:codeCommon({theme}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:theme.typography.size.s1}}})),ArgJsDoc=({tags})=>{let params=(tags.params||[]).filter(x=>x.description),hasDisplayableParams=params.length!==0,hasDisplayableDeprecated=tags.deprecated!=null,hasDisplayableReturns=tags.returns!=null&&tags.returns.description!=null;return !hasDisplayableParams&&!hasDisplayableReturns&&!hasDisplayableDeprecated?null:React17.createElement(React17.Fragment,null,React17.createElement(Table,null,React17.createElement("tbody",null,hasDisplayableDeprecated&&React17.createElement("tr",{key:"deprecated"},React17.createElement("td",{colSpan:2},React17.createElement("strong",null,"Deprecated"),": ",tags.deprecated)),hasDisplayableParams&&params.map(x=>React17.createElement("tr",{key:x.name},React17.createElement("td",null,React17.createElement("code",null,x.name)),React17.createElement("td",null,x.description))),hasDisplayableReturns&&React17.createElement("tr",{key:"returns"},React17.createElement("td",null,React17.createElement("code",null,"Returns")),React17.createElement("td",null,tags.returns.description)))))};var ITEMS_BEFORE_EXPANSION=8,Summary=styled.div(({isExpanded})=>({display:"flex",flexDirection:isExpanded?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Text=styled.span(codeCommon,({theme,simple=!1})=>({flex:"0 0 auto",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...simple&&{background:"transparent",border:"0 none",paddingLeft:0}})),ExpandButton=styled.button(({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,marginBottom:"4px",background:"none",border:"none"})),Expandable=styled.div(codeCommon,({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,fontSize:theme.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),Detail=styled.div(({theme,width})=>({width,minWidth:200,maxWidth:800,padding:15,fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),ArrowIcon=styled(Icons)({height:10,width:10,minWidth:10,marginLeft:4}),EmptyArg=()=>React17.createElement("span",null,"-"),ArgText=({text,simple})=>React17.createElement(Text,{simple},text),calculateDetailWidth=memoize(1e3)(detail=>{let lines=detail.split(/\r?\n/);return `${Math.max(...lines.map(x=>x.length))}ch`}),getSummaryItems=summary=>{if(!summary)return [summary];let summaryItems=summary.split("|").map(value2=>value2.trim());return uniq(summaryItems)},renderSummaryItems=(summaryItems,isExpanded=!0)=>{let items=summaryItems;return isExpanded||(items=summaryItems.slice(0,ITEMS_BEFORE_EXPANSION)),items.map(item=>React17.createElement(ArgText,{key:item,text:item===""?'""':item}))},ArgSummary=({value:value2,initialExpandedArgs})=>{let{summary,detail}=value2,[isOpen,setIsOpen]=useState(!1),[isExpanded,setIsExpanded]=useState(initialExpandedArgs||!1);if(summary==null)return null;let summaryAsString=typeof summary.toString=="function"?summary.toString():summary;if(detail==null){if(/[(){}[\]<>]/.test(summaryAsString))return React17.createElement(ArgText,{text:summaryAsString});let summaryItems=getSummaryItems(summaryAsString),itemsCount=summaryItems.length;return itemsCount>ITEMS_BEFORE_EXPANSION?React17.createElement(Summary,{isExpanded},renderSummaryItems(summaryItems,isExpanded),React17.createElement(ExpandButton,{onClick:()=>setIsExpanded(!isExpanded)},isExpanded?"Show less...":`Show ${itemsCount-ITEMS_BEFORE_EXPANSION} more...`)):React17.createElement(Summary,null,renderSummaryItems(summaryItems))}return React17.createElement(WithTooltipPure,{closeOnOutsideClick:!0,placement:"bottom",visible:isOpen,onVisibleChange:isVisible=>{setIsOpen(isVisible);},tooltip:React17.createElement(Detail,{width:calculateDetailWidth(detail)},React17.createElement(SyntaxHighlighter,{language:"jsx",format:!1},detail))},React17.createElement(Expandable,{className:"sbdocs-expandable"},React17.createElement("span",null,summaryAsString),React17.createElement(ArrowIcon,{icon:isOpen?"arrowup":"arrowdown"})))},ArgValue=({value:value2,initialExpandedArgs})=>value2==null?React17.createElement(EmptyArg,null):React17.createElement(ArgSummary,{value:value2,initialExpandedArgs});var Label=styled.label(({theme})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:theme.boolean.background,borderRadius:"3em",padding:1,input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${theme.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:theme.typography.size.s1,fontWeight:theme.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:transparentize(.5,theme.color.defaultText),background:"transparent","&:hover":{boxShadow:`${opacify(.3,theme.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${opacify(.05,theme.appBorderColor)} 0 0 0 2px inset`,color:opacify(1,theme.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:theme.boolean.selectedBackground,boxShadow:theme.base==="light"?`${opacify(.1,theme.appBorderColor)} 0 0 2px`:`${theme.appBorderColor} 0 0 0 1px`,color:theme.color.defaultText,padding:"7px 15px"}})),parse=value2=>value2==="true",BooleanControl=({name,value:value2,onChange,onBlur,onFocus})=>{let onSetFalse=useCallback(()=>onChange(!1),[onChange]);if(value2===void 0)return React17.createElement(Form.Button,{id:getControlSetterButtonId(name),onClick:onSetFalse},"Set boolean");let controlId=getControlId(name),parsedValue=typeof value2=="string"?parse(value2):value2;return React17.createElement(Label,{htmlFor:controlId,"aria-label":name},React17.createElement("input",{id:controlId,type:"checkbox",onChange:e=>onChange(e.target.checked),checked:parsedValue,role:"switch",name,onBlur,onFocus}),React17.createElement("span",{"aria-hidden":"true"},"False"),React17.createElement("span",{"aria-hidden":"true"},"True"))};var parseDate=value2=>{let[year,month,day]=value2.split("-"),result=new Date;return result.setFullYear(parseInt(year,10),parseInt(month,10)-1,parseInt(day,10)),result},parseTime=value2=>{let[hours,minutes]=value2.split(":"),result=new Date;return result.setHours(parseInt(hours,10)),result.setMinutes(parseInt(minutes,10)),result},formatDate=value2=>{let date=new Date(value2),year=`000${date.getFullYear()}`.slice(-4),month=`0${date.getMonth()+1}`.slice(-2),day=`0${date.getDate()}`.slice(-2);return `${year}-${month}-${day}`},formatTime=value2=>{let date=new Date(value2),hours=`0${date.getHours()}`.slice(-2),minutes=`0${date.getMinutes()}`.slice(-2);return `${hours}:${minutes}`},FlexSpaced=styled.div(({theme})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:theme.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),DateControl=({name,value:value2,onChange,onFocus,onBlur})=>{let[valid,setValid]=useState(!0),dateRef=useRef(),timeRef=useRef();useEffect(()=>{valid!==!1&&(dateRef&&dateRef.current&&(dateRef.current.value=formatDate(value2)),timeRef&&timeRef.current&&(timeRef.current.value=formatTime(value2)));},[value2]);let onDateChange=e=>{let parsed=parseDate(e.target.value),result=new Date(value2);result.setFullYear(parsed.getFullYear(),parsed.getMonth(),parsed.getDate());let time=result.getTime();time&&onChange(time),setValid(!!time);},onTimeChange=e=>{let parsed=parseTime(e.target.value),result=new Date(value2);result.setHours(parsed.getHours()),result.setMinutes(parsed.getMinutes());let time=result.getTime();time&&onChange(time),setValid(!!time);},controlId=getControlId(name);return React17.createElement(FlexSpaced,null,React17.createElement(Form.Input,{type:"date",max:"9999-12-31",ref:dateRef,id:`${controlId}-date`,name:`${controlId}-date`,onChange:onDateChange,onFocus,onBlur}),React17.createElement(Form.Input,{type:"time",id:`${controlId}-time`,name:`${controlId}-time`,ref:timeRef,onChange:onTimeChange,onFocus,onBlur}),valid?null:React17.createElement("div",null,"invalid"))};var Wrapper2=styled.label({display:"flex"}),parse2=value2=>{let result=parseFloat(value2);return Number.isNaN(result)?void 0:result},format=value2=>value2!=null?String(value2):"",NumberControl=({name,value:value2,onChange,min,max,step,onBlur,onFocus})=>{let[inputValue,setInputValue]=useState(typeof value2=="number"?value2:""),[forceVisible,setForceVisible]=useState(!1),[parseError,setParseError]=useState(null),handleChange=useCallback(event=>{setInputValue(event.target.value);let result=parseFloat(event.target.value);Number.isNaN(result)?setParseError(new Error(`'${event.target.value}' is not a number`)):(onChange(result),setParseError(null));},[onChange,setParseError]),onForceVisible=useCallback(()=>{setInputValue("0"),onChange(0),setForceVisible(!0);},[setForceVisible]),htmlElRef=useRef(null);return useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),useEffect(()=>{inputValue!==(typeof value2=="number"?value2:"")&&setInputValue(value2);},[value2]),!forceVisible&&value2===void 0?React17.createElement(Form.Button,{id:getControlSetterButtonId(name),onClick:onForceVisible},"Set number"):React17.createElement(Wrapper2,null,React17.createElement(Form.Input,{ref:htmlElRef,id:getControlId(name),type:"number",onChange:handleChange,size:"flex",placeholder:"Edit number...",value:inputValue,valid:parseError?"error":null,autoFocus:forceVisible,name,min,max,step,onFocus,onBlur}))};var selectedKey=(value2,options)=>{let entry=options&&Object.entries(options).find(([_key,val])=>val===value2);return entry?entry[0]:void 0},selectedKeys=(value2,options)=>value2&&options?Object.entries(options).filter(entry=>value2.includes(entry[1])).map(entry=>entry[0]):[],selectedValues=(keys,options)=>keys&&options&&keys.map(key=>options[key]);var Wrapper3=styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}}),Text2=styled.span({}),Label2=styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),CheckboxControl=({name,options,value:value2,onChange,isInline})=>{if(!options)return logger.warn(`Checkbox with no options: ${name}`),React17.createElement(React17.Fragment,null,"-");let initial=selectedKeys(value2,options),[selected,setSelected]=useState(initial),handleChange=e=>{let option=e.target.value,updated=[...selected];updated.includes(option)?updated.splice(updated.indexOf(option),1):updated.push(option),onChange(selectedValues(updated,options)),setSelected(updated);};useEffect(()=>{setSelected(selectedKeys(value2,options));},[value2]);let controlId=getControlId(name);return React17.createElement(Wrapper3,{isInline},Object.keys(options).map((key,index)=>{let id=`${controlId}-${index}`;return React17.createElement(Label2,{key:id,htmlFor:id},React17.createElement("input",{type:"checkbox",id,name:id,value:key,onChange:handleChange,checked:selected?.includes(key)}),React17.createElement(Text2,null,key))}))};var Wrapper4=styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}}),Text3=styled.span({}),Label3=styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),RadioControl=({name,options,value:value2,onChange,isInline})=>{if(!options)return logger.warn(`Radio with no options: ${name}`),React17.createElement(React17.Fragment,null,"-");let selection=selectedKey(value2,options),controlId=getControlId(name);return React17.createElement(Wrapper4,{isInline},Object.keys(options).map((key,index)=>{let id=`${controlId}-${index}`;return React17.createElement(Label3,{key:id,htmlFor:id},React17.createElement("input",{type:"radio",id,name:id,value:key,onChange:e=>onChange(options[e.currentTarget.value]),checked:key===selection}),React17.createElement(Text3,null,key))}))};var styleResets={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},OptionsSelect=styled.select(styleResets,({theme})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:theme.input.color||"inherit",background:theme.input.background,borderRadius:theme.input.borderRadius,boxShadow:`${theme.input.border} 0 0 0 1px inset`,fontSize:theme.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${theme.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:theme.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),SelectWrapper=styled.span(({theme})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:theme.textMutedColor,path:{fill:theme.textMutedColor}}})),NO_SELECTION="Choose option...",SingleSelect=({name,value:value2,options,onChange})=>{let handleChange=e=>{onChange(options[e.currentTarget.value]);},selection=selectedKey(value2,options)||NO_SELECTION,controlId=getControlId(name);return React17.createElement(SelectWrapper,null,React17.createElement(Icons,{icon:"arrowdown"}),React17.createElement(OptionsSelect,{id:controlId,value:selection,onChange:handleChange},React17.createElement("option",{key:"no-selection",disabled:!0},NO_SELECTION),Object.keys(options).map(key=>React17.createElement("option",{key,value:key},key))))},MultiSelect=({name,value:value2,options,onChange})=>{let handleChange=e=>{let selection2=Array.from(e.currentTarget.options).filter(option=>option.selected).map(option=>option.value);onChange(selectedValues(selection2,options));},selection=selectedKeys(value2,options),controlId=getControlId(name);return React17.createElement(SelectWrapper,null,React17.createElement(OptionsSelect,{id:controlId,multiple:!0,value:selection,onChange:handleChange},Object.keys(options).map(key=>React17.createElement("option",{key,value:key},key))))},SelectControl=props=>{let{name,options}=props;return options?props.isMulti?React17.createElement(MultiSelect,{...props}):React17.createElement(SingleSelect,{...props}):(logger.warn(`Select with no options: ${name}`),React17.createElement(React17.Fragment,null,"-"))};var normalizeOptions=(options,labels)=>Array.isArray(options)?options.reduce((acc,item)=>(acc[labels?.[item]||String(item)]=item,acc),{}):options,Controls={check:CheckboxControl,"inline-check":CheckboxControl,radio:RadioControl,"inline-radio":RadioControl,select:SelectControl,"multi-select":SelectControl},OptionsControl=props=>{let{type="select",labels,argType}=props,normalized={...props,options:argType?normalizeOptions(argType.options,labels):{},isInline:type.includes("inline"),isMulti:type.includes("multi")},Control=Controls[type];if(Control)return React17.createElement(Control,{...normalized});throw new Error(`Unknown options type: ${type}`)};var VALUE="value",KEY="key";var ERROR="Error",OBJECT="Object",ARRAY="Array",STRING="String",NUMBER="Number",BOOLEAN="Boolean",DATE="Date",NULL="Null",UNDEFINED="Undefined",FUNCTION="Function",SYMBOL="Symbol";var ADD_DELTA_TYPE="ADD_DELTA_TYPE",REMOVE_DELTA_TYPE="REMOVE_DELTA_TYPE",UPDATE_DELTA_TYPE="UPDATE_DELTA_TYPE";function getObjectType(obj){return obj!==null&&typeof obj=="object"&&!Array.isArray(obj)&&typeof obj[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(obj).slice(8,-1)}function isComponentWillChange(oldValue,newValue){let oldType=getObjectType(oldValue),newType=getObjectType(newValue);return (oldType==="Function"||newType==="Function")&&newType!==oldType}var JsonAddValue=class extends Component{constructor(props){super(props),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this);}componentDidMount(){let{inputRefKey,inputRefValue}=this.state,{onlyValue}=this.props;inputRefKey&&typeof inputRefKey.focus=="function"&&inputRefKey.focus(),onlyValue&&inputRefValue&&typeof inputRefValue.focus=="function"&&inputRefValue.focus(),document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.onSubmit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.props.handleCancel()));}onSubmit(){let{handleAdd,onlyValue,onSubmitValueParser,keyPath,deep}=this.props,{inputRefKey,inputRefValue}=this.state,result={};if(!onlyValue){if(!inputRefKey.value)return;result.key=inputRefKey.value;}result.newValue=onSubmitValueParser(!1,keyPath,deep,result.key,inputRefValue.value),handleAdd(result);}refInputKey(node){this.state.inputRefKey=node;}refInputValue(node){this.state.inputRefValue=node;}render(){let{handleCancel,onlyValue,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep}=this.props,addButtonElementLayout=cloneElement(addButtonElement,{onClick:this.onSubmit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:handleCancel}),inputElementValue=inputElementGenerator(VALUE,keyPath,deep),inputElementValueLayout=cloneElement(inputElementValue,{placeholder:"Value",ref:this.refInputValue}),inputElementKeyLayout=null;if(!onlyValue){let inputElementKey=inputElementGenerator(KEY,keyPath,deep);inputElementKeyLayout=cloneElement(inputElementKey,{placeholder:"Key",ref:this.refInputKey});}return React17.createElement("span",{className:"rejt-add-value-node"},inputElementKeyLayout,inputElementValueLayout,cancelButtonElementLayout,addButtonElementLayout)}};JsonAddValue.defaultProps={onlyValue:!1,addButtonElement:React17.createElement("button",null,"+"),cancelButtonElement:React17.createElement("button",null,"c")};var JsonArray=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={data:props.data,name:props.name,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleRemoveItem(index){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[index];beforeRemoveAction(index,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key:index,oldValue,type:REMOVE_DELTA_TYPE};data.splice(index,1),this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleAddValueAdd({newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(data.length,keyPath,deep,newValue).then(()=>{let newData=[...data,newValue];this.setState({data:newData}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],newData),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key:newData.length-1,newValue});}).catch(logger4.error);}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeUpdateAction(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve(void 0);}).catch(reject);})}renderCollapsed(){let{name,data,keyPath,deep}=this.state,{handleRemove,readOnly,getStyle,dataType,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return React17.createElement("span",{className:"rejt-collapsed"},React17.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"[...] ",data.length," ",data.length===1?"item":"items"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,addFormVisible,nextDeep}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,delimiter,ul,addForm}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus}),onlyValue=!0,startObject="[",endObject="]";return React17.createElement("span",{className:"rejt-not-collapsed"},React17.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},startObject),!addFormVisible&&addItemButton,React17.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},data.map((item,index)=>React17.createElement(JsonNode,{key:index,name:index.toString(),data:item,keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveItem(index),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}))),!isReadOnly&&addFormVisible&&React17.createElement("div",{className:"rejt-add-form",style:addForm},React17.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React17.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},endObject),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{dataType,getStyle}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React17.createElement("div",{className:"rejt-array-node"},React17.createElement("span",{onClick:this.handleCollapseMode},React17.createElement("span",{className:"rejt-name",style:style.name},name," :"," ")),value2)}};JsonArray.defaultProps={keyPath:[],deep:0,minusMenuElement:React17.createElement("span",null," - "),plusMenuElement:React17.createElement("span",null," + ")};var JsonFunctionValue=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,readOnlyResult=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!readOnlyResult&&typeof inputRef.focus=="function"&&inputRef.focus();}componentDidMount(){document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value);handleUpdateValue({value:newValue,key:name}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,textareaElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),result=null,minusElement=null,resultOnlyResult=readOnly(name,originalValue,keyPath,deep,dataType);if(editEnabled&&!resultOnlyResult){let textareaElement=textareaElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),textareaElementLayout=cloneElement(textareaElement,{ref:this.refInput,defaultValue:originalValue});result=React17.createElement("span",{className:"rejt-edit-form",style:style.editForm},textareaElementLayout," ",cancelButtonElementLayout,editButtonElementLayout),minusElement=null;}else {result=React17.createElement("span",{className:"rejt-value",style:style.value,onClick:resultOnlyResult?null:this.handleEditMode},value2);let minusMenuLayout=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});minusElement=resultOnlyResult?null:minusMenuLayout;}return React17.createElement("li",{className:"rejt-function-value-node",style:style.li},React17.createElement("span",{className:"rejt-name",style:style.name},name," :"," "),result,minusElement)}};JsonFunctionValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>{},editButtonElement:React17.createElement("button",null,"e"),cancelButtonElement:React17.createElement("button",null,"c"),minusMenuElement:React17.createElement("span",null," - ")};var JsonNode=class extends Component{constructor(props){super(props),this.state={data:props.data,name:props.name,keyPath:props.keyPath,deep:props.deep};}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}render(){let{data,name,keyPath,deep}=this.state,{isCollapsed,handleRemove,handleUpdateValue,onUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,readOnlyTrue=()=>!0,dataType=getObjectType(data);switch(dataType){case ERROR:return React17.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly:readOnlyTrue,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case OBJECT:return React17.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case ARRAY:return React17.createElement(JsonArray,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case STRING:return React17.createElement(JsonValue,{name,value:`"${data}"`,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NUMBER:return React17.createElement(JsonValue,{name,value:data,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case BOOLEAN:return React17.createElement(JsonValue,{name,value:data?"true":"false",originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case DATE:return React17.createElement(JsonValue,{name,value:data.toISOString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NULL:return React17.createElement(JsonValue,{name,value:"null",originalValue:"null",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case UNDEFINED:return React17.createElement(JsonValue,{name,value:"undefined",originalValue:"undefined",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case FUNCTION:return React17.createElement(JsonFunctionValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,textareaElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case SYMBOL:return React17.createElement(JsonValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});default:return null}}};JsonNode.defaultProps={keyPath:[],deep:0};var JsonObject=class extends Component{constructor(props){super(props);let keyPath=props.deep===-1?[]:[...props.keyPath,props.name];this.state={name:props.name,data:props.data,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleAddValueAdd({key,newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(key,keyPath,deep,newValue).then(()=>{data[key]=newValue,this.setState({data}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key,newValue});}).catch(logger4.error);}handleRemoveValue(key){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeRemoveAction(key,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key,oldValue,type:REMOVE_DELTA_TYPE};delete data[key],this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeUpdateAction(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve();}).catch(reject);})}renderCollapsed(){let{name,keyPath,deep,data}=this.state,{handleRemove,readOnly,dataType,getStyle,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return React17.createElement("span",{className:"rejt-collapsed"},React17.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"{...}"," ",keyList.length," ",keyList.length===1?"key":"keys"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,nextDeep,addFormVisible}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,addForm,ul,delimiter}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus}),list=keyList.map(key=>React17.createElement(JsonNode,{key,name:key,data:data[key],keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveValue(key),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser})),startObject="{",endObject="}";return React17.createElement("span",{className:"rejt-not-collapsed"},React17.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},startObject),!isReadOnly&&addItemButton,React17.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},list),!isReadOnly&&addFormVisible&&React17.createElement("div",{className:"rejt-add-form",style:addForm},React17.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React17.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},endObject),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{getStyle,dataType}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React17.createElement("div",{className:"rejt-object-node"},React17.createElement("span",{onClick:this.handleCollapseMode},React17.createElement("span",{className:"rejt-name",style:style.name},name," :"," ")),value2)}};JsonObject.defaultProps={keyPath:[],deep:0,minusMenuElement:React17.createElement("span",null," - "),plusMenuElement:React17.createElement("span",null," + ")};var JsonValue=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,isReadOnly=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!isReadOnly&&typeof inputRef.focus=="function"&&inputRef.focus();}componentDidMount(){document.addEventListener("keydown",this.onKeydown);}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value);handleUpdateValue({value:newValue,key:name}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,inputElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),isReadOnly=readOnly(name,originalValue,keyPath,deep,dataType),isEditing=editEnabled&&!isReadOnly,inputElement=inputElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),inputElementLayout=cloneElement(inputElement,{ref:this.refInput,defaultValue:JSON.stringify(originalValue)}),minusMenuLayout=cloneElement(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});return React17.createElement("li",{className:"rejt-value-node",style:style.li},React17.createElement("span",{className:"rejt-name",style:style.name},name," : "),isEditing?React17.createElement("span",{className:"rejt-edit-form",style:style.editForm},inputElementLayout," ",cancelButtonElementLayout,editButtonElementLayout):React17.createElement("span",{className:"rejt-value",style:style.value,onClick:isReadOnly?null:this.handleEditMode},String(value2)),!isReadOnly&&!isEditing&&minusMenuLayout)}};JsonValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>Promise.resolve(),editButtonElement:React17.createElement("button",null,"e"),cancelButtonElement:React17.createElement("button",null,"c"),minusMenuElement:React17.createElement("span",null," - ")};var object={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},array={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},value={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}};function parse3(string){let result=string;if(result.indexOf("function")===0)return (0, eval)(`(${result})`);try{result=JSON.parse(string);}catch{}return result}var JsonTree=class extends Component{constructor(props){super(props),this.state={data:props.data,rootName:props.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data||props.rootName!==state.rootName?{data:props.data,rootName:props.rootName}:null}onUpdate(key,data){this.setState({data}),this.props.onFullyUpdate(data);}removeRoot(){this.onUpdate(null,null);}render(){let{data,rootName}=this.state,{isCollapsed,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElement,textareaElement,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser,fallback=null}=this.props,dataType=getObjectType(data),readOnlyFunction=readOnly;getObjectType(readOnly)==="Boolean"&&(readOnlyFunction=()=>readOnly);let inputElementFunction=inputElement;inputElement&&getObjectType(inputElement)!=="Function"&&(inputElementFunction=()=>inputElement);let textareaElementFunction=textareaElement;return textareaElement&&getObjectType(textareaElement)!=="Function"&&(textareaElementFunction=()=>textareaElement),dataType==="Object"||dataType==="Array"?React17.createElement("div",{className:"rejt-tree"},React17.createElement(JsonNode,{data,name:rootName,deep:-1,isCollapsed,onUpdate:this.onUpdate,onDeltaUpdate,readOnly:readOnlyFunction,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator:inputElementFunction,textareaElementGenerator:textareaElementFunction,minusMenuElement,plusMenuElement,handleRemove:this.removeRoot,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser})):fallback}};JsonTree.defaultProps={rootName:"root",isCollapsed:(keyPath,deep)=>deep!==-1,getStyle:(keyName,data,keyPath,deep,dataType)=>{switch(dataType){case"Object":case"Error":return object;case"Array":return array;default:return value}},readOnly:()=>!1,onFullyUpdate:()=>{},onDeltaUpdate:()=>{},beforeRemoveAction:()=>Promise.resolve(),beforeAddAction:()=>Promise.resolve(),beforeUpdateAction:()=>Promise.resolve(),logger:{error:()=>{}},onSubmitValueParser:(isEditMode,keyPath,deep,name,rawValue)=>parse3(rawValue),inputElement:()=>React17.createElement("input",null),textareaElement:()=>React17.createElement("textarea",null),fallback:null};var {window:globalWindow2}=global,Wrapper5=styled.div(({theme})=>({position:"relative",display:"flex",".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:theme.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:theme.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:theme.color.lighter,borderColor:theme.appBorderColor}})),Button=styled.button(({theme,primary})=>({border:0,height:20,margin:1,borderRadius:4,background:primary?theme.color.secondary:"transparent",color:primary?theme.color.lightest:theme.color.dark,fontWeight:primary?"bold":"normal",cursor:"pointer",order:primary?"initial":9})),ActionIcon=styled(Icons)(({theme,icon,disabled})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?"not-allowed":"pointer",color:theme.textMutedColor,"&:hover":disabled?{}:{color:icon==="subtract"?theme.color.negative:theme.color.ancillary},"svg + &":{marginLeft:0}})),Input=styled.input(({theme,placeholder})=>({outline:0,margin:placeholder?1:"1px 0",padding:"3px 4px",color:theme.color.defaultText,background:theme.background.app,border:`1px solid ${theme.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:placeholder==="Key"?80:120,"&:focus":{border:`1px solid ${theme.color.secondary}`}})),RawButton=styled(IconButton)(({theme})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:theme.background.bar,border:`1px solid ${theme.appBorderColor}`,borderRadius:3,color:theme.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),RawInput=styled(Form.Textarea)(({theme})=>({flex:1,padding:"7px 6px",fontFamily:theme.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:theme.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),ENTER_EVENT={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},dispatchEnterKey=event=>{event.currentTarget.dispatchEvent(new globalWindow2.KeyboardEvent("keydown",ENTER_EVENT));},selectValue=event=>{event.currentTarget.select();},getCustomStyleFunction=theme=>()=>({name:{color:theme.color.secondary},collapsed:{color:theme.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),ObjectControl=({name,value:value2,onChange})=>{let theme=useTheme(),data=useMemo(()=>value2&&cloneDeep(value2),[value2]),hasData=data!=null,[showRaw,setShowRaw]=useState(!hasData),[parseError,setParseError]=useState(null),updateRaw=useCallback(raw=>{try{raw&&onChange(JSON.parse(raw)),setParseError(void 0);}catch(e){setParseError(e);}},[onChange]),[forceVisible,setForceVisible]=useState(!1),onForceVisible=useCallback(()=>{onChange({}),setForceVisible(!0);},[setForceVisible]),htmlElRef=useRef(null);if(useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),!hasData)return React17.createElement(Form.Button,{id:getControlSetterButtonId(name),onClick:onForceVisible},"Set object");let rawJSONForm=React17.createElement(RawInput,{ref:htmlElRef,id:getControlId(name),name,defaultValue:value2===null?"":JSON.stringify(value2,null,2),onBlur:event=>updateRaw(event.target.value),placeholder:"Edit JSON string...",autoFocus:forceVisible,valid:parseError?"error":null});return React17.createElement(Wrapper5,null,["Object","Array"].includes(getObjectType(data))&&React17.createElement(RawButton,{href:"#",onClick:e=>{e.preventDefault(),setShowRaw(v=>!v);}},React17.createElement(Icons,{icon:showRaw?"eyeclose":"eye"}),React17.createElement("span",null,"RAW")),showRaw?rawJSONForm:React17.createElement(JsonTree,{data,rootName:name,onFullyUpdate:onChange,getStyle:getCustomStyleFunction(theme),cancelButtonElement:React17.createElement(Button,{type:"button"},"Cancel"),editButtonElement:React17.createElement(Button,{type:"submit"},"Save"),addButtonElement:React17.createElement(Button,{type:"submit",primary:!0},"Save"),plusMenuElement:React17.createElement(ActionIcon,{icon:"add"}),minusMenuElement:React17.createElement(ActionIcon,{icon:"subtract"}),inputElement:(_,__,___,key)=>key?React17.createElement(Input,{onFocus:selectValue,onBlur:dispatchEnterKey}):React17.createElement(Input,null),fallback:rawJSONForm}))};var RangeInput=styled.input(({theme,min,max,value:value2})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:"grab",appearance:"none",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${darken(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:rgba(theme.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:theme.color.secondary,boxShadow:`0 0px 5px 0px ${theme.color.secondary}`}},"&::-moz-range-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:"grab",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${darken(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, 
            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${theme.input.background}`,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),RangeLabel=styled.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums"}),RangeCurrentAndMaxLabel=styled(RangeLabel)(({numberOFDecimalsPlaces,max})=>({width:`${numberOFDecimalsPlaces+max.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),RangeWrapper=styled.div({display:"flex",alignItems:"center",width:"100%"});function getNumberOfDecimalPlaces(number){let match=number.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return match?Math.max(0,(match[1]?match[1].length:0)-(match[2]?+match[2]:0)):0}var RangeControl=({name,value:value2,onChange,min=0,max=100,step=1,onBlur,onFocus})=>{let handleChange=event=>{onChange(parse2(event.target.value));},hasValue=value2!==void 0,numberOFDecimalsPlaces=useMemo(()=>getNumberOfDecimalPlaces(step),[step]);return React17.createElement(RangeWrapper,null,React17.createElement(RangeLabel,null,min),React17.createElement(RangeInput,{id:getControlId(name),type:"range",onChange:handleChange,name,value:value2,min,max,step,onFocus,onBlur}),React17.createElement(RangeCurrentAndMaxLabel,{numberOFDecimalsPlaces,max},hasValue?value2.toFixed(numberOFDecimalsPlaces):"--"," / ",max))};var Wrapper6=styled.label({display:"flex"}),MaxLength=styled.div(({isMaxed})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:isMaxed?"red":void 0})),TextControl=({name,value:value2,onChange,onFocus,onBlur,maxLength})=>{let handleChange=event=>{onChange(event.target.value);},[forceVisible,setForceVisible]=useState(!1),onForceVisible=useCallback(()=>{onChange(""),setForceVisible(!0);},[setForceVisible]);if(value2===void 0)return React17.createElement(Form.Button,{id:getControlSetterButtonId(name),onClick:onForceVisible},"Set string");let isValid=typeof value2=="string";return React17.createElement(Wrapper6,null,React17.createElement(Form.Textarea,{id:getControlId(name),maxLength,onChange:handleChange,size:"flex",placeholder:"Edit string...",autoFocus:forceVisible,valid:isValid?null:"error",name,value:isValid?value2:"",onFocus,onBlur}),maxLength&&React17.createElement(MaxLength,{isMaxed:value2?.length===maxLength},value2?.length??0," / ",maxLength))};var FileInput=styled(Form.Input)({padding:10});function revokeOldUrls(urls){urls.forEach(url=>{url.startsWith("blob:")&&URL.revokeObjectURL(url);});}var FilesControl=({onChange,name,accept="image/*",value:value2})=>{let inputElement=useRef(null);function handleFileChange(e){if(!e.target.files)return;let fileUrls=Array.from(e.target.files).map(file=>URL.createObjectURL(file));onChange(fileUrls),revokeOldUrls(value2);}return useEffect(()=>{value2==null&&inputElement.current&&(inputElement.current.value=null);},[value2,name]),React17.createElement(FileInput,{ref:inputElement,id:getControlId(name),type:"file",name,multiple:!0,onChange:handleFileChange,accept,size:"flex"})};var LazyColorControl=lazy(()=>import('./Color-6VNJS4EI.mjs')),ColorControl=props=>React17.createElement(Suspense,{fallback:React17.createElement("div",null)},React17.createElement(LazyColorControl,{...props}));var Controls2={array:ObjectControl,object:ObjectControl,boolean:BooleanControl,color:ColorControl,date:DateControl,number:NumberControl,check:OptionsControl,"inline-check":OptionsControl,radio:OptionsControl,"inline-radio":OptionsControl,select:OptionsControl,"multi-select":OptionsControl,range:RangeControl,text:TextControl,file:FilesControl},NoControl=()=>React17.createElement(React17.Fragment,null,"-"),ArgControl=({row,arg,updateArgs,isHovered})=>{let{key,control}=row,[isFocused,setFocused]=useState(!1),[boxedValue,setBoxedValue]=useState({value:arg});useEffect(()=>{isFocused||setBoxedValue({value:arg});},[isFocused,arg]);let onChange=useCallback(argVal=>(setBoxedValue({value:argVal}),updateArgs({[key]:argVal}),argVal),[updateArgs,key]),onBlur=useCallback(()=>setFocused(!1),[]),onFocus=useCallback(()=>setFocused(!0),[]);if(!control||control.disable)return isHovered?React17.createElement(Link,{href:"https://storybook.js.org/docs/react/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):React17.createElement(NoControl,null);let props={name:key,argType:row,value:boxedValue.value,onChange,onBlur,onFocus},Control=Controls2[control.type]||NoControl;return React17.createElement(Control,{...props,...control,controlType:control.type})};var Name=styled.span({fontWeight:"bold"}),Required=styled.span(({theme})=>({color:theme.color.negative,fontFamily:theme.typography.fonts.mono,cursor:"help"})),Description=styled.div(({theme})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:theme.color.secondary}},code:{...codeCommon({theme}),fontSize:12,fontFamily:theme.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Type=styled.div(({theme,hasDescription})=>({color:theme.base==="light"?transparentize(.1,theme.color.defaultText):transparentize(.2,theme.color.defaultText),marginTop:hasDescription?4:0})),TypeWithJsDoc=styled.div(({theme,hasDescription})=>({color:theme.base==="light"?transparentize(.1,theme.color.defaultText):transparentize(.2,theme.color.defaultText),marginTop:hasDescription?12:0,marginBottom:12})),StyledTd=styled.td(({theme,expandable})=>({paddingLeft:expandable?"40px !important":"20px !important"})),ArgRow=props=>{let[isHovered,setIsHovered]=useState(!1),{row,updateArgs,compact,expandable,initialExpandedArgs}=props,{name,description}=row,table=row.table||{},type=table.type||row.type,defaultValue=table.defaultValue||row.defaultValue,required=row.type?.required,hasDescription=description!=null&&description!=="";return React17.createElement("tr",{onMouseEnter:()=>setIsHovered(!0),onMouseLeave:()=>setIsHovered(!1)},React17.createElement(StyledTd,{expandable},React17.createElement(Name,null,name),required?React17.createElement(Required,{title:"Required"},"*"):null),compact?null:React17.createElement("td",null,hasDescription&&React17.createElement(Description,null,React17.createElement(Markdown,null,description)),table.jsDocTags!=null?React17.createElement(React17.Fragment,null,React17.createElement(TypeWithJsDoc,{hasDescription},React17.createElement(ArgValue,{value:type,initialExpandedArgs})),React17.createElement(ArgJsDoc,{tags:table.jsDocTags})):React17.createElement(Type,{hasDescription},React17.createElement(ArgValue,{value:type,initialExpandedArgs}))),compact?null:React17.createElement("td",null,React17.createElement(ArgValue,{value:defaultValue,initialExpandedArgs})),updateArgs?React17.createElement("td",null,React17.createElement(ArgControl,{...props,isHovered})):null)};var ExpanderIcon=styled(Icons)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base==="light"?transparentize(.25,theme.color.defaultText):transparentize(.3,theme.color.defaultText),border:"none",display:"inline-block"})),FlexWrapper=styled.span(({theme})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),Section=styled.td(({theme})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s1-1,color:theme.base==="light"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText),background:`${theme.background.app} !important`,"& ~ td":{background:`${theme.background.app} !important`}})),Subsection=styled.td(({theme})=>({position:"relative",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,background:theme.background.app})),StyledTd2=styled.td(()=>({position:"relative"})),StyledTr=styled.tr(({theme})=>({"&:hover > td":{backgroundColor:`${lighten(.005,theme.background.app)} !important`,boxShadow:`${theme.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),ClickIntercept=styled.button(()=>({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"})),SectionRow=({level="section",label,children,initialExpanded=!0,colSpan=3})=>{let[expanded,setExpanded]=useState(initialExpanded),Level=level==="subsection"?Subsection:Section,itemCount=children?.length||0,caption=level==="subsection"?`${itemCount} item${itemCount!==1?"s":""}`:"",icon=expanded?"arrowdown":"arrowright",helperText=`${expanded?"Hide":"Show"} ${level==="subsection"?itemCount:label} item${itemCount!==1?"s":""}`;return React17.createElement(React17.Fragment,null,React17.createElement(StyledTr,{title:helperText},React17.createElement(Level,{colSpan:1},React17.createElement(ClickIntercept,{onClick:e=>setExpanded(!expanded),tabIndex:0},helperText),React17.createElement(FlexWrapper,null,React17.createElement(ExpanderIcon,{icon}),label)),React17.createElement(StyledTd2,{colSpan:colSpan-1},React17.createElement(ClickIntercept,{onClick:e=>setExpanded(!expanded),tabIndex:-1,style:{outline:"none"}},helperText),expanded?null:caption)),expanded?children:null)};var Row=styled.div(({theme})=>({display:"flex",gap:16,borderBottom:`1px solid ${theme.appBorderColor}`,"&:last-child":{borderBottom:0}})),Column=styled.div(({numColumn})=>({display:"flex",flexDirection:"column",flex:numColumn||1,gap:5,padding:"12px 20px"})),SkeletonText=styled.div(({theme,width,height})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,width:width||"100%",height:height||16,borderRadius:3})),columnWidth=[2,4,2,2],Skeleton=()=>React17.createElement(React17.Fragment,null,React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:"30%"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:"60%"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:"80%"}),React17.createElement(SkeletonText,{width:"30%"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:"60%"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:"80%"}),React17.createElement(SkeletonText,{width:"30%"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:"60%"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:"80%"}),React17.createElement(SkeletonText,{width:"30%"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:"60%"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:"60%"}))));var Wrapper7=styled.div(({inAddonPanel,theme})=>({height:inAddonPanel?"100%":"auto",display:"flex",border:inAddonPanel?"none":`1px solid ${theme.appBorderColor}`,borderRadius:inAddonPanel?0:theme.appBorderRadius,padding:inAddonPanel?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:theme.background.content,boxShadow:"rgba(0, 0, 0, 0.10) 0 1px 3px 0"})),Content=styled.div({display:"flex",flexDirection:"column",gap:4,maxWidth:415}),Title2=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,textAlign:"center",color:theme.textColor})),Description2=styled.div(({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s2-1,textAlign:"center",color:theme.textMutedColor})),Links=styled.div(({theme})=>({display:"flex",fontSize:theme.typography.size.s2-1,gap:25})),Divider=styled.div(({theme})=>({width:1,height:16,backgroundColor:theme.appBorderColor})),Empty=({inAddonPanel})=>{let[isLoading,setIsLoading]=useState(!0);return useEffect(()=>{let load=setTimeout(()=>{setIsLoading(!1);},100);return ()=>clearTimeout(load)},[]),isLoading?null:React17.createElement(Wrapper7,{inAddonPanel},React17.createElement(Content,null,React17.createElement(Title2,null,inAddonPanel?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated"),React17.createElement(Description2,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically.")),React17.createElement(Links,null,inAddonPanel&&React17.createElement(React17.Fragment,null,React17.createElement(Link,{href:"https://youtu.be/0gOfS6K0x0E",target:"_blank",withArrow:!0},React17.createElement(Icons,{icon:"video"})," Watch 5m video"),React17.createElement(Divider,null),React17.createElement(Link,{href:"https://storybook.js.org/docs/react/essentials/controls",target:"_blank",withArrow:!0},"Read docs")),!inAddonPanel&&React17.createElement(Link,{href:"https://storybook.js.org/docs/react/essentials/controls",target:"_blank",withArrow:!0},"Learn how to set that up")))};var TableWrapper=styled.table(({theme,compact,inAddonPanel})=>({"&&":{borderSpacing:0,color:theme.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:theme.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:inAddonPanel?0:25,marginBottom:inAddonPanel?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...compact?null:{width:"35%"}},"td:nth-of-type(3)":{...compact?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...compact?null:{width:"25%"}},th:{color:theme.base==="light"?transparentize(.25,theme.color.defaultText):transparentize(.45,theme.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:inAddonPanel?0:1,marginRight:inAddonPanel?0:1,tbody:{...inAddonPanel?null:{filter:theme.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:theme.background.content,borderTop:`1px solid ${theme.appBorderColor}`},...inAddonPanel?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${theme.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${theme.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${theme.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${theme.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:theme.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:theme.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:theme.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:theme.appBorderRadius}}}}})),StyledIconButton=styled(IconButton)(({theme})=>({color:theme.barTextColor,margin:"-4px -12px -4px 0"})),ControlHeadingWrapper=styled.span({display:"flex",justifyContent:"space-between"});var sortFns={alpha:(a,b)=>a.name.localeCompare(b.name),requiredFirst:(a,b)=>+!!b.type?.required-+!!a.type?.required||a.name.localeCompare(b.name),none:void 0},groupRows=(rows,sort)=>{let sections={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!rows)return sections;Object.entries(rows).forEach(([key,row])=>{let{category,subcategory}=row?.table||{};if(category){let section=sections.sections[category]||{ungrouped:[],subsections:{}};if(!subcategory)section.ungrouped.push({key,...row});else {let subsection=section.subsections[subcategory]||[];subsection.push({key,...row}),section.subsections[subcategory]=subsection;}sections.sections[category]=section;}else if(subcategory){let subsection=sections.ungroupedSubsections[subcategory]||[];subsection.push({key,...row}),sections.ungroupedSubsections[subcategory]=subsection;}else sections.ungrouped.push({key,...row});});let sortFn=sortFns[sort],sortSubsection=record=>sortFn?Object.keys(record).reduce((acc,cur)=>({...acc,[cur]:record[cur].sort(sortFn)}),{}):record;return {ungrouped:sections.ungrouped.sort(sortFn),ungroupedSubsections:sortSubsection(sections.ungroupedSubsections),sections:Object.keys(sections.sections).reduce((acc,cur)=>({...acc,[cur]:{ungrouped:sections.sections[cur].ungrouped.sort(sortFn),subsections:sortSubsection(sections.sections[cur].subsections)}}),{})}},safeIncludeConditionalArg=(row,args,globals)=>{try{return includeConditionalArg(row,args,globals)}catch(err){return once.warn(err.message),!1}},ArgsTable=props=>{let{updateArgs,resetArgs,compact,inAddonPanel,initialExpandedArgs,sort="none",isLoading}=props;if("error"in props){let{error}=props;return React17.createElement(EmptyBlock,null,error,"\xA0",React17.createElement(Link,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},"Read the docs"))}if(isLoading)return React17.createElement(Skeleton,null);let{rows,args,globals}="rows"in props&&props,groups=groupRows(pickBy(rows,row=>!row?.table?.disable&&safeIncludeConditionalArg(row,args||{},globals||{})),sort),hasNoUngrouped=groups.ungrouped.length===0,hasNoSections=Object.entries(groups.sections).length===0,hasNoUngroupedSubsections=Object.entries(groups.ungroupedSubsections).length===0;if(hasNoUngrouped&&hasNoSections&&hasNoUngroupedSubsections)return React17.createElement(Empty,{inAddonPanel});let colSpan=1;updateArgs&&(colSpan+=1),compact||(colSpan+=2);let expandable=Object.keys(groups.sections).length>0,common={updateArgs,compact,inAddonPanel,initialExpandedArgs};return React17.createElement(ResetWrapper,null,React17.createElement(TableWrapper,{compact,inAddonPanel,className:"docblock-argstable sb-unstyled"},React17.createElement("thead",{className:"docblock-argstable-head"},React17.createElement("tr",null,React17.createElement("th",null,React17.createElement("span",null,"Name")),compact?null:React17.createElement("th",null,React17.createElement("span",null,"Description")),compact?null:React17.createElement("th",null,React17.createElement("span",null,"Default")),updateArgs?React17.createElement("th",null,React17.createElement(ControlHeadingWrapper,null,"Control"," ",!isLoading&&resetArgs&&React17.createElement(StyledIconButton,{onClick:()=>resetArgs(),title:"Reset controls"},React17.createElement(Icons,{icon:"undo","aria-hidden":!0})))):null)),React17.createElement("tbody",{className:"docblock-argstable-body"},groups.ungrouped.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(groups.ungroupedSubsections).map(([subcategory,subsection])=>React17.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))),Object.entries(groups.sections).map(([category,section])=>React17.createElement(SectionRow,{key:category,label:category,level:"section",colSpan},section.ungrouped.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(section.subsections).map(([subcategory,subsection])=>React17.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))))))))};var TabbedArgsTable=({tabs,...props})=>{let entries=Object.entries(tabs);return entries.length===1?React17.createElement(ArgsTable,{...entries[0][1],...props}):React17.createElement(TabsState,null,entries.map(entry=>{let[label,table]=entry,id=`prop_table_div_${label}`;return React17.createElement("div",{key:id,id,title:label},({active})=>active?React17.createElement(ArgsTable,{key:`prop_table_${label}`,...table,...props}):null)}))};var Label4=styled.div(({theme})=>({marginRight:30,fontSize:`${theme.typography.size.s1}px`,color:theme.base==="light"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),Sample=styled.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),TypeSpecimen=styled.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),Wrapper8=styled.div(withReset,({theme})=>({...getBlockBackgroundStyle(theme),margin:"25px 0 40px",padding:"30px 20px"})),Typeset=({fontFamily,fontSizes,fontWeight,sampleText,...props})=>React17.createElement(Wrapper8,{...props,className:"docblock-typeset sb-unstyled"},fontSizes.map(size=>React17.createElement(TypeSpecimen,{key:size},React17.createElement(Label4,null,size),React17.createElement(Sample,{style:{fontFamily,fontSize:size,fontWeight,lineHeight:1.2}},sampleText||"Was he a beast if music could move him so?"))));var ItemTitle=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,color:theme.color.defaultText})),ItemSubtitle=styled.div(({theme})=>({color:theme.base==="light"?transparentize(.2,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),ItemDescription=styled.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),SwatchLabel=styled.div(({theme})=>({flex:1,textAlign:"center",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,lineHeight:1,overflow:"hidden",color:theme.base==="light"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),SwatchLabels=styled.div({display:"flex",flexDirection:"row"}),Swatch=styled.div(({background})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background,content:'""'}})),SwatchColors=styled.div(({theme})=>({...getBlockBackgroundStyle(theme),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),SwatchSpecimen=styled.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),Swatches=styled.div({flex:1,display:"flex",flexDirection:"row"}),Item=styled.div({display:"flex",alignItems:"flex-start"}),ListName=styled.div({flex:"0 0 30%"}),ListSwatches=styled.div({flex:1}),ListHeading=styled.div(({theme})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:theme.typography.weight.bold,color:theme.base==="light"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),List=styled.div(({theme})=>({fontSize:theme.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"}));function renderSwatch(color,index){return React17.createElement(Swatch,{key:`${color}-${index}`,title:color,background:color})}function renderSwatchLabel(color,index,colorDescription){return React17.createElement(SwatchLabel,{key:`${color}-${index}`,title:color},React17.createElement("div",null,color,colorDescription&&React17.createElement("span",null,colorDescription)))}function renderSwatchSpecimen(colors){return Array.isArray(colors)?React17.createElement(SwatchSpecimen,null,React17.createElement(SwatchColors,null,colors.map((color,index)=>renderSwatch(color,index))),React17.createElement(SwatchLabels,null,colors.map((color,index)=>renderSwatchLabel(color,index)))):React17.createElement(SwatchSpecimen,null,React17.createElement(SwatchColors,null,Object.values(colors).map((color,index)=>renderSwatch(color,index))),React17.createElement(SwatchLabels,null,Object.keys(colors).map((color,index)=>renderSwatchLabel(color,index,colors[color]))))}var ColorItem=({title,subtitle,colors})=>React17.createElement(Item,null,React17.createElement(ItemDescription,null,React17.createElement(ItemTitle,null,title),React17.createElement(ItemSubtitle,null,subtitle)),React17.createElement(Swatches,null,renderSwatchSpecimen(colors))),ColorPalette=({children,...props})=>React17.createElement(ResetWrapper,null,React17.createElement(List,{...props,className:"docblock-colorpalette sb-unstyled"},React17.createElement(ListHeading,null,React17.createElement(ListName,null,"Name"),React17.createElement(ListSwatches,null,"Swatches")),children));var ItemLabel=styled.div(({theme})=>({fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,color:theme.color.defaultText,marginLeft:10,lineHeight:1.2})),ItemSpecimen=styled.div(({theme})=>({...getBlockBackgroundStyle(theme),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),Item2=styled.div({display:"inline-flex",flexDirection:"row",alignItems:"center",flex:"0 1 calc(20% - 10px)",minWidth:120,margin:"0px 10px 30px 0"}),List2=styled.div({display:"flex",flexFlow:"row wrap"}),IconItem=({name,children})=>React17.createElement(Item2,null,React17.createElement(ItemSpecimen,null,children),React17.createElement(ItemLabel,null,name)),IconGallery=({children,...props})=>React17.createElement(ResetWrapper,null,React17.createElement(List2,{...props,className:"docblock-icongallery sb-unstyled"},children));var anchorBlockIdFromId=storyId=>`anchor--${storyId}`,Anchor=({storyId,children})=>React17.createElement("div",{id:anchorBlockIdFromId(storyId),className:"sb-anchor"},children);global&&global.__DOCS_CONTEXT__===void 0&&(global.__DOCS_CONTEXT__=createContext(null),global.__DOCS_CONTEXT__.displayName="DocsContext");var DocsContext=global?global.__DOCS_CONTEXT__:createContext(null);var useOf=(moduleExportOrType,validTypes)=>useContext(DocsContext).resolveOf(moduleExportOrType,validTypes);function extractComponentArgTypes(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");return extractArgTypes(component)}function getArgTypesFromResolved(resolved,props){if(resolved.type==="component"){let{component,projectAnnotations:{parameters:parameters2}}=resolved;return {argTypes:extractComponentArgTypes(component,parameters2),parameters:parameters2}}if(resolved.type==="meta"){let{preparedMeta:{argTypes:argTypes2,parameters:parameters2}}=resolved;return {argTypes:argTypes2,parameters:parameters2}}let{story:{argTypes,parameters}}=resolved;return {argTypes,parameters}}var ArgTypes=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let resolved=useOf(of||"meta"),{argTypes,parameters}=getArgTypesFromResolved(resolved),argTypesParameters=parameters.docs?.argTypes||{},include=props.include??argTypesParameters.include,exclude=props.exclude??argTypesParameters.exclude,sort=props.sort??argTypesParameters.sort,filteredArgTypes=filterArgTypes(argTypes,include,exclude);return React17.createElement(ArgsTable,{rows:filteredArgTypes,sort})};var PRIMARY_STORY="^";var titleCase=str2=>str2.split("-").map(part=>part.charAt(0).toUpperCase()+part.slice(1)).join(""),getComponentName=component=>{if(component)return typeof component=="string"?component.includes("-")?titleCase(component):component:component.__docgenInfo&&component.__docgenInfo.displayName?component.__docgenInfo.displayName:component.name};function scrollToElement(element,block="start"){element.scrollIntoView({behavior:"smooth",block,inline:"nearest"});}function useStory(storyId,context){let stories=useStories([storyId],context);return stories&&stories[0]}function useStories(storyIds,context){let[storiesById,setStories]=useState({});return useEffect(()=>{Promise.all(storyIds.map(async storyId=>{let story=await context.loadStory(storyId);setStories(current=>current[storyId]===story?current:{...current,[storyId]:story});}));}),storyIds.map(storyId=>{if(storiesById[storyId])return storiesById[storyId];try{return context.storyById(storyId)}catch{return null}})}var useArgs=(storyId,context)=>{let storyContext=context.getStoryContext(context.storyById()),[args,setArgs]=useState(storyContext.args);useEffect(()=>{let cb=changed=>{changed.storyId===storyId&&setArgs(changed.args);};return context.channel.on(STORY_ARGS_UPDATED,cb),()=>context.channel.off(STORY_ARGS_UPDATED,cb)},[storyId]);let updateArgs=useCallback(updatedArgs=>context.channel.emit(UPDATE_STORY_ARGS,{storyId,updatedArgs}),[storyId]),resetArgs=useCallback(argNames=>context.channel.emit(RESET_STORY_ARGS,{storyId,argNames}),[storyId]);return [args,updateArgs,resetArgs]},useGlobals=context=>{let storyContext=context.getStoryContext(context.storyById()),[globals,setGlobals]=useState(storyContext.globals);return useEffect(()=>{let cb=changed=>{setGlobals(changed.globals);};return context.channel.on(GLOBALS_UPDATED,cb),()=>context.channel.off(GLOBALS_UPDATED,cb)},[]),[globals]},extractComponentArgTypes2=(component,parameters,include,exclude)=>{let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");let argTypes=extractArgTypes(component);return argTypes=filterArgTypes(argTypes,include,exclude),argTypes},isShortcut=value2=>value2&&[PRIMARY_STORY].includes(value2),getComponent=(props={},component)=>{let{of}=props,{story}=props;if(isShortcut(of)||isShortcut(story))return component||null;if(!of)throw new Error("No component found.");return of},addComponentTabs=(tabs,components2,parameters,include,exclude,sort)=>({...tabs,...mapValues(components2,comp=>({rows:extractComponentArgTypes2(comp,parameters,include,exclude),sort}))}),StoryTable=props=>{let context=useContext(DocsContext),{story:storyName,component,subcomponents,showComponent,include,exclude,sort}=props;try{let storyId;switch(storyName){case PRIMARY_STORY:{storyId=context.storyById().id;break}default:storyId=context.storyIdByName(storyName);}let story=useStory(storyId,context),[args,updateArgs,resetArgs]=useArgs(storyId,context),[globals]=useGlobals(context);if(!story)return React17.createElement(ArgsTable,{isLoading:!0,updateArgs,resetArgs});let argTypes=filterArgTypes(story.argTypes,include,exclude),mainLabel=getComponentName(component)||"Story",tabs={[mainLabel]:{rows:argTypes,args,globals,updateArgs,resetArgs}},storyHasArgsWithControls=argTypes&&Object.values(argTypes).find(v=>!!v?.control);if(storyHasArgsWithControls||(updateArgs=null,resetArgs=null,tabs={}),component&&(!storyHasArgsWithControls||showComponent)&&(tabs=addComponentTabs(tabs,{[mainLabel]:component},story.parameters,include,exclude)),subcomponents){if(Array.isArray(subcomponents))throw new Error("Unexpected subcomponents array. Expected an object whose keys are tab labels and whose values are components.");tabs=addComponentTabs(tabs,subcomponents,story.parameters,include,exclude);}return React17.createElement(TabbedArgsTable,{tabs,sort})}catch(err){return React17.createElement(ArgsTable,{error:err.message})}},ComponentsTable=props=>{let{components:components2,include,exclude,sort,parameters}=props,tabs=addComponentTabs({},components2,parameters,include,exclude);return React17.createElement(TabbedArgsTable,{tabs,sort})},ArgsTable2=props=>{deprecate(dedent2`The ArgsTable doc block is deprecated. Instead use the ArgTypes doc block for static tables or the Controls doc block for tables with controls.
    
  Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#argstable-block
  `);let context=useContext(DocsContext),parameters,component,subcomponents;try{({parameters,component,subcomponents}=context.storyById());}catch{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");({projectAnnotations:{parameters}}=context.resolveOf(of,["component"]));}let{include,exclude,components:components2,sort:sortProp}=props,{story:storyName}=props,sort=sortProp||parameters.controls?.sort,main=getComponent(props,component);if(storyName)return React17.createElement(StoryTable,{...props,component:main,subcomponents,sort});if(!components2&&!subcomponents){let mainProps;try{mainProps={rows:extractComponentArgTypes2(main,parameters,include,exclude)};}catch(err){mainProps={error:err.message};}return React17.createElement(ArgsTable,{...mainProps,sort})}if(components2)return React17.createElement(ComponentsTable,{...props,components:components2,sort,parameters});let mainLabel=getComponentName(main);return React17.createElement(ComponentsTable,{...props,components:{[mainLabel]:main,...subcomponents},sort,parameters})};ArgsTable2.defaultProps={of:PRIMARY_STORY};function argsHash(args){return stringify(args)}var SourceContext=createContext({sources:{}}),UNKNOWN_ARGS_HASH="--unknown--",SourceContainer=({children,channel})=>{let[sources,setSources]=useState({});return useEffect(()=>{let handleSnippetRendered=(idOrEvent,inputSource=null,inputFormat=!1)=>{let{id,args=void 0,source,format:format2}=typeof idOrEvent=="string"?{id:idOrEvent,source:inputSource,format:inputFormat}:idOrEvent,hash=args?argsHash(args):UNKNOWN_ARGS_HASH;setSources(current=>({...current,[id]:{...current[id],[hash]:{code:source,format:format2}}}));};return channel.on(SNIPPET_RENDERED,handleSnippetRendered),()=>channel.off(SNIPPET_RENDERED,handleSnippetRendered)},[]),React17.createElement(SourceContext.Provider,{value:{sources}},children)};var SourceState=(SourceState2=>(SourceState2.OPEN="open",SourceState2.CLOSED="closed",SourceState2.NONE="none",SourceState2))(SourceState||{}),getSourceState=stories=>{let states=stories.map(story=>story.parameters.docs?.source?.state).filter(Boolean);return states.length===0?"closed":states[0]},getStorySource=(storyId,args,sourceContext)=>{let{sources}=sourceContext,sourceMap=sources?.[storyId];return sourceMap?.[argsHash(args)]||sourceMap?.[UNKNOWN_ARGS_HASH]||{code:""}},getSnippet=({snippet,storyContext,typeFromProps,transformFromProps})=>{let{__isArgsStory:isArgsStory}=storyContext.parameters,sourceParameters=storyContext.parameters.docs?.source||{},type=typeFromProps||sourceParameters.type||SourceType.AUTO;if(sourceParameters.code!==void 0)return sourceParameters.code;let code=type===SourceType.DYNAMIC||type===SourceType.AUTO&&snippet&&isArgsStory?snippet:sourceParameters.originalSource||"";return sourceParameters.transformSource&&deprecate(dedent2`The \`transformSource\` parameter at \`parameters.docs.source.transformSource\` is deprecated, please use \`parameters.docs.source.transform\` instead. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#source-block
  `),storyContext.parameters.docs?.transformSource&&deprecate(dedent2`The \`transformSource\` parameter at \`parameters.docs.transformSource\` is deprecated, please use \`parameters.docs.source.transform\` instead. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#source-block
  `),storyContext.parameters.jsx?.transformSource&&deprecate(dedent2`The \`transformSource\` parameter at \`parameters.jsx.transformSource\` is deprecated, please use \`parameters.docs.source.transform\` instead. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#source-block
  `),(transformFromProps??sourceParameters.transform??sourceParameters.transformSource??storyContext.parameters.docs?.transformSource??storyContext.parameters.jsx?.transformSource)?.(code,storyContext)||code},useSourceProps=(props,docsContext,sourceContext)=>{let storyIds=props.ids||(props.id?[props.id]:[]),storiesFromIds=useStories(storyIds,docsContext),stories=storiesFromIds,{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");if(of)stories=[docsContext.resolveOf(of,["story"]).story];else if(stories.length===0)try{stories=[docsContext.storyById()];}catch{}if(!storiesFromIds.every(Boolean))return {error:"Oh no! The source is not available.",state:"none"};let sourceParameters=stories[0]?.parameters?.docs?.source||{},{code}=props,format2=props.format??sourceParameters.format,language=props.language??sourceParameters.language??"jsx",dark=props.dark??sourceParameters.dark??!1;code||(code=stories.map((story,index)=>{if(!story)return "";let storyContext=docsContext.getStoryContext(story),argsForSource=props.__forceInitialArgs?storyContext.initialArgs:storyContext.unmappedArgs,source=getStorySource(story.id,argsForSource,sourceContext);return index===0&&(format2=source.format??story.parameters.docs?.source?.format??!1),getSnippet({snippet:source.code,storyContext:{...storyContext,args:argsForSource},typeFromProps:props.type,transformFromProps:props.transform})}).join(`

`));let state=getSourceState(stories);return code?{code,format:format2,language,dark,state}:{error:"Oh no! The source is not available.",state}},Source2=props=>{props.id&&deprecate(dedent2`The \`id\` prop on Source is deprecated, please use the \`of\` prop instead to reference a story. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#source-block
  `),props.ids&&deprecate(dedent2`The \`ids\` prop on Source is deprecated, please use the \`of\` prop instead to reference a story. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#source-block
  `);let sourceContext=useContext(SourceContext),docsContext=useContext(DocsContext),{state,...sourceProps}=useSourceProps(props,docsContext,sourceContext);return React17.createElement(Source,{...sourceProps})};var getStoryId2=(props,context)=>{let{id,of,meta,story}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");if(id)return deprecate(dedent2`Referencing stories by \`id\` is deprecated, please use \`of\` instead. 
    
      Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#story-block'`),id;let{name}=props;return name?(deprecate(dedent2`Referencing stories by \`name\` is deprecated, please use \`of\` instead. 
    
      Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#story-block'`),context.storyIdByName(name)):(story&&deprecate(dedent2`The \`story\` prop is deprecated, please export your stories from CSF files and reference them with \`of={}\`.

      Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#story-block'`),meta&&context.referenceMeta(meta,!1),context.resolveOf(of||story||"story",["story"]).story.id)},getStoryProps=(props,story,context)=>{let{parameters={}}=story||{},{docs={}}=parameters,storyParameters=docs.story||{};if(docs.disable)return null;let{inlineStories,iframeHeight}=docs;typeof inlineStories<"u"&&deprecate(dedent2`The \`docs.inlineStories\` parameter is deprecated, use \`docs.story.inline\` instead. 
    
      Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#autodocs-changes'
    `);let inline=props.inline??storyParameters.inline??inlineStories??!1;if(typeof iframeHeight<"u"&&deprecate(dedent2`The \`docs.iframeHeight\` parameter is deprecated, use \`docs.story.iframeHeight\` instead. 
    
      Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#autodocs-changes'
    `),inline){let height2=props.height??storyParameters.height,autoplay=props.autoplay??storyParameters.autoplay??!1;return {story,inline:!0,height:height2,autoplay,forceInitialArgs:!!props.__forceInitialArgs,primary:!!props.__primary,renderStoryToElement:context.renderStoryToElement}}let height=props.height??storyParameters.height??storyParameters.iframeHeight??iframeHeight??"100px";return {story,inline:!1,height,primary:!!props.__primary}},Story2=(props={__forceInitialArgs:!1,__primary:!1})=>{let context=useContext(DocsContext),storyId=getStoryId2(props,context),story=useStory(storyId,context);if(!story)return React17.createElement(StorySkeleton,null);let storyProps=getStoryProps(props,story,context);return storyProps?React17.createElement(Story,{...storyProps}):null};var useDeprecatedPreviewProps=({withSource,mdxSource,children,layout:layoutProp,...props},docsContext,sourceContext)=>{let storyIds=Children.toArray(children).filter(c=>c.props&&(c.props.id||c.props.name||c.props.of)).map(c=>getStoryId2(c.props,docsContext)),stories=useStories(storyIds,docsContext),isLoading=stories.some(s=>!s),sourceProps=useSourceProps({...mdxSource?{code:decodeURI(mdxSource)}:{ids:storyIds},...props.of&&{of:props.of}},docsContext,sourceContext);if(withSource==="none")return {isLoading,previewProps:props};let layout=layoutProp;return Children.forEach(children,child=>{layout||(layout=child?.props?.parameters?.layout);}),stories.forEach(story=>{layout||!story||(layout=story?.parameters.layout??story.parameters.docs?.canvas?.layout);}),{isLoading,previewProps:{...props,layout:layout??"padded",withSource:sourceProps,isExpanded:(withSource||sourceProps.state)==="open"}}},Canvas=props=>{let docsContext=useContext(DocsContext),sourceContext=useContext(SourceContext),{children,of,source}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let{isLoading,previewProps}=useDeprecatedPreviewProps(props,docsContext,sourceContext),story,sourceProps,hookError;try{({story}=useOf(of||"story",["story"]));}catch(error){children||(hookError=error);}try{sourceProps=useSourceProps({...source,...of&&{of}},docsContext,sourceContext);}catch(error){children||(hookError=error);}if(hookError)throw hookError;if(props.withSource&&deprecate(dedent2`Setting source state with \`withSource\` is deprecated, please use \`sourceState\` with 'hidden', 'shown' or 'none' instead. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#canvas-block
    `),props.mdxSource&&deprecate(dedent2`Setting source code with \`mdxSource\` is deprecated, please use source={{code: '...'}} instead. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#canvas-block
    `),(props.isColumn!==void 0||props.columns!==void 0)&&deprecate(dedent2`\`isColumn\` and \`columns\` props are deprecated as the Canvas block now only supports showing a single story. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#canvas-block
    `),children)return deprecate(dedent2`Passing children to Canvas is deprecated, please use the \`of\` prop instead to reference a story. 
    
    Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#canvas-block
  `),isLoading?React17.createElement(PreviewSkeleton,null):React17.createElement(Preview,{...previewProps},children);let layout=props.layout??story.parameters.layout??story.parameters.docs?.canvas?.layout??"padded",withToolbar=props.withToolbar??story.parameters.docs?.canvas?.withToolbar??!1,additionalActions=props.additionalActions??story.parameters.docs?.canvas?.additionalActions,sourceState=props.sourceState??story.parameters.docs?.canvas?.sourceState??"hidden",className=props.className??story.parameters.docs?.canvas?.className;return React17.createElement(Preview,{withSource:sourceState==="none"?void 0:sourceProps,isExpanded:sourceState==="shown",withToolbar,additionalActions,className,layout},React17.createElement(Story2,{of:of||story.moduleExport,meta:props.meta,...props.story}))};var useGlobals2=(story,context)=>{let storyContext=context.getStoryContext(story),[globals,setGlobals]=useState(storyContext.globals);return useEffect(()=>{let onGlobalsUpdated=changed=>{setGlobals(changed.globals);};return context.channel.on(GLOBALS_UPDATED,onGlobalsUpdated),()=>context.channel.off(GLOBALS_UPDATED,onGlobalsUpdated)},[context.channel]),[globals]};var useArgs2=(story,context)=>{let result=useArgsIfDefined(story,context);if(!result)throw new Error("No result when story was defined");return result},useArgsIfDefined=(story,context)=>{let storyContext=story?context.getStoryContext(story):{args:{}},{id:storyId}=story||{id:"none"},[args,setArgs]=useState(storyContext.args);useEffect(()=>{let onArgsUpdated=changed=>{changed.storyId===storyId&&setArgs(changed.args);};return context.channel.on(STORY_ARGS_UPDATED,onArgsUpdated),()=>context.channel.off(STORY_ARGS_UPDATED,onArgsUpdated)},[storyId,context.channel]);let updateArgs=useCallback(updatedArgs=>context.channel.emit(UPDATE_STORY_ARGS,{storyId,updatedArgs}),[storyId,context.channel]),resetArgs=useCallback(argNames=>context.channel.emit(RESET_STORY_ARGS,{storyId,argNames}),[storyId,context.channel]);return story&&[args,updateArgs,resetArgs]};var Controls3=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let context=useContext(DocsContext),{story}=context.resolveOf(of||"story",["story"]),{parameters,argTypes}=story,controlsParameters=parameters.docs?.controls||{},include=props.include??controlsParameters.include,exclude=props.exclude??controlsParameters.exclude,sort=props.sort??controlsParameters.sort,[args,updateArgs,resetArgs]=useArgs2(story,context),[globals]=useGlobals2(story,context),filteredArgTypes=filterArgTypes(argTypes,include,exclude);return React17.createElement(ArgsTable,{rows:filteredArgTypes,args,globals,updateArgs,resetArgs,sort})};var {document:document2}=global,assertIsFn=val=>{if(typeof val!="function")throw new Error(`Expected story function, got: ${val}`);return val},AddContext=props=>{let{children,...rest}=props,parentContext=React17.useContext(DocsContext);return React17.createElement(DocsContext.Provider,{value:{...parentContext,...rest}},children)},CodeOrSourceMdx=({className,children,...rest})=>{if(typeof className!="string"&&(typeof children!="string"||!children.match(/[\n\r]/g)))return React17.createElement(Code,null,children);let language=className&&className.split("-");return React17.createElement(Source,{language:language&&language[1]||"plaintext",format:!1,code:children,...rest})};function navigate(context,url){context.channel.emit(NAVIGATE_URL,url);}var A=components.a,AnchorInPage=({hash,children})=>{let context=useContext(DocsContext);return React17.createElement(A,{href:hash,target:"_self",onClick:event=>{let id=hash.substring(1);document2.getElementById(id)&&navigate(context,hash);}},children)},AnchorMdx=props=>{let{href,target,children,...rest}=props,context=useContext(DocsContext);if(href){if(href.startsWith("#"))return React17.createElement(AnchorInPage,{hash:href},children);if(target!=="_blank"&&!href.startsWith("https://"))return React17.createElement(A,{href,onClick:event=>{event.button===0&&!event.altKey&&!event.ctrlKey&&!event.metaKey&&!event.shiftKey&&(event.preventDefault(),navigate(context,event.currentTarget.getAttribute("href")));},target,...rest},children)}return React17.createElement(A,{...props})},SUPPORTED_MDX_HEADERS=["h1","h2","h3","h4","h5","h6"],OcticonHeaders=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:styled(headerType)({"& svg":{position:"relative",top:"-0.1em",visibility:"hidden"},"&:hover svg":{visibility:"visible"}})}),{}),OcticonAnchor=styled.a(()=>({float:"left",lineHeight:"inherit",paddingRight:"10px",marginLeft:"-24px",color:"inherit"})),HeaderWithOcticonAnchor=({as,id,children,...rest})=>{let context=useContext(DocsContext),OcticonHeader=OcticonHeaders[as],hash=`#${id}`;return React17.createElement(OcticonHeader,{id,...rest},React17.createElement(OcticonAnchor,{"aria-hidden":"true",href:hash,tabIndex:-1,target:"_self",onClick:event=>{document2.getElementById(id)&&navigate(context,hash);}},React17.createElement(Icons,{icon:"link"})),children)},HeaderMdx=props=>{let{as,id,children,...rest}=props;if(id)return React17.createElement(HeaderWithOcticonAnchor,{as,id,...rest},children);let Component4=as,{as:omittedAs,...withoutAs}=props;return React17.createElement(Component4,{...nameSpaceClassNames(withoutAs,as)})},HeadersMdx=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:props=>React17.createElement(HeaderMdx,{as:headerType,...props})}),{});var Markdown2=props=>{if(!props.children)return null;if(typeof props.children!="string")throw new Error(dedent2`The Markdown block only accepts children as a single string, but children were of type: '${typeof props.children}'
        This is often caused by not wrapping the child in a template string.
        
        This is invalid:
        <Markdown>
          # Some heading
          A paragraph
        </Markdown>

        Instead do:
        <Markdown>
        {\`
          # Some heading
          A paragraph
        \`}
        </Markdown>
      `);return React17.createElement(Markdown,{...props,options:{forceBlock:!0,overrides:{code:CodeOrSourceMdx,a:AnchorMdx,...HeadersMdx,...props?.options?.overrides},...props?.options}})};var DescriptionType=(DescriptionType2=>(DescriptionType2.INFO="info",DescriptionType2.NOTES="notes",DescriptionType2.DOCGEN="docgen",DescriptionType2.AUTO="auto",DescriptionType2))(DescriptionType||{}),DEPRECATION_MIGRATION_LINK="https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#description-block-parametersnotes-and-parametersinfo",getNotes=notes=>notes&&(typeof notes=="string"?notes:str(notes.markdown)||str(notes.text)),getInfo=info=>info&&(typeof info=="string"?info:str(info.text)),noDescription=component=>null,getDescriptionFromResolvedOf=resolvedOf=>{switch(resolvedOf.type){case"story":return resolvedOf.story.parameters.docs?.description?.story||null;case"meta":{let{parameters,component}=resolvedOf.preparedMeta,metaDescription=parameters.docs?.description?.component;return metaDescription||parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}case"component":{let{component,projectAnnotations:{parameters}}=resolvedOf;return parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}default:throw new Error(`Unrecognized module type resolved from 'useOf', got: ${resolvedOf.type}`)}},getDescriptionFromDeprecatedProps=({type,markdown,children},{storyById})=>{let{component,parameters}=storyById();if(children||markdown)return children||markdown;let{notes,info,docs}=parameters;(notes||info)&&deprecate(`Using 'parameters.notes' or 'parameters.info' properties to describe stories is deprecated. See ${DEPRECATION_MIGRATION_LINK}`);let{extractComponentDescription=noDescription,description}=docs||{},componentDescriptionParameter=description?.component;if(componentDescriptionParameter)return componentDescriptionParameter;switch(type){case"info":return getInfo(info);case"notes":return getNotes(notes);case"docgen":case"auto":default:return extractComponentDescription(component,{component,...parameters})}},DescriptionContainer=props=>{let{of,type,markdown:markdownProp,children}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let context=useContext(DocsContext),resolvedOf=useOf(of||"meta"),markdown;return type||markdownProp||children?markdown=getDescriptionFromDeprecatedProps(props,context):markdown=getDescriptionFromResolvedOf(resolvedOf),type&&deprecate(`Manually specifying description type is deprecated. See ${DEPRECATION_MIGRATION_LINK}`),markdownProp&&deprecate(`The 'markdown' prop on the Description block is deprecated. See ${DEPRECATION_MIGRATION_LINK}`),children&&deprecate(`The 'children' prop on the Description block is deprecated. See ${DEPRECATION_MIGRATION_LINK}`),markdown?React17.createElement(Markdown2,null,markdown):null};var Wrapper9=styled.div(({theme})=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),Content2=styled.div(({theme})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${theme.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:theme.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:theme.color.secondary,textDecoration:"none"}})),Heading=styled.p(({theme})=>({fontWeight:600,fontSize:"0.875em",color:theme.textColor,textTransform:"uppercase",marginBottom:10})),OptionalTitle=({title})=>title===null?null:typeof title=="string"?React17.createElement(Heading,null,title):title,TableOfContents=({title,disable,headingSelector,contentsSelector,ignoreSelector,unsafeTocbotOptions})=>(useEffect(()=>{let configuration={tocSelector:".toc-wrapper",contentSelector:contentsSelector??".sbdocs-content",headingSelector:headingSelector??"h3",ignoreSelector:ignoreSelector??".docs-story *, .skip-toc",headingsOffset:40,scrollSmoothOffset:-40,orderedList:!1,onClick:()=>!1,...unsafeTocbotOptions},timeout=setTimeout(()=>tocbot.init(configuration),100);return ()=>{clearTimeout(timeout),tocbot.destroy();}},[disable]),React17.createElement(React17.Fragment,null,React17.createElement(Wrapper9,null,disable?null:React17.createElement(Content2,null,React17.createElement(OptionalTitle,{title:title||null}),React17.createElement("div",{className:"toc-wrapper"})))));var {document:document3,window:globalWindow3}=global,DocsContainer=({context,theme,children})=>{let toc;try{toc=context.resolveOf("meta",["meta"]).preparedMeta.parameters?.docs?.toc;}catch{toc=context?.projectAnnotations?.parameters?.docs?.toc;}return useEffect(()=>{let url;try{if(url=new URL(globalWindow3.parent.location.toString()),url.hash){let element=document3.getElementById(url.hash.substring(1));element&&setTimeout(()=>{scrollToElement(element);},200);}}catch{}}),React17.createElement(DocsContext.Provider,{value:context},React17.createElement(SourceContainer,{channel:context.channel},React17.createElement(ThemeProvider,{theme:ensure(theme)},React17.createElement(DocsPageWrapper,{toc:toc?React17.createElement(TableOfContents,{className:"sbdocs sbdocs-toc--custom",...toc}):null},children))))};var STORY_KIND_PATH_SEPARATOR=/\s*\/\s*/,extractTitle=title=>{let groups=title.trim().split(STORY_KIND_PATH_SEPARATOR);return groups&&groups[groups.length-1]||title},Title3=({children})=>{let context=useContext(DocsContext),content=children||extractTitle(context.storyById().title);return content?React17.createElement(Title,{className:"sbdocs-title sb-unstyled"},content):null};var Subtitle2=({children})=>{let docsContext=useContext(DocsContext),content=children||docsContext.storyById().parameters?.componentSubtitle;return content?React17.createElement(Subtitle,{className:"sbdocs-subtitle sb-unstyled"},content):null};var Subheading=({children,disableAnchor})=>{if(disableAnchor||typeof children!="string")return React17.createElement(H3,null,children);let tagID=children.toLowerCase().replace(/[^a-z0-9]/gi,"-");return React17.createElement(HeaderMdx,{as:"h3",id:tagID},children)};var DocsStory=({of,expanded=!0,withToolbar:withToolbarProp=!1,__forceInitialArgs=!1,__primary=!1})=>{let{story}=useOf(of||"story",["story"]),withToolbar=story.parameters.docs?.canvas?.withToolbar??withToolbarProp;return React17.createElement(Anchor,{storyId:story.id},expanded&&React17.createElement(React17.Fragment,null,React17.createElement(Subheading,null,story.name),React17.createElement(DescriptionContainer,{of})),React17.createElement(Canvas,{of,withToolbar,story:{__forceInitialArgs,__primary},source:{__forceInitialArgs}}))};var Primary=props=>{let{name,of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let docsContext=useContext(DocsContext),story;if(of&&(story=useOf(of||"meta",["meta"]).csfFile.stories[0]||null),!story){let storyId=name&&docsContext.storyIdByName(name);story=docsContext.storyById(storyId);}return name&&deprecate(dedent2`\`name\` prop is deprecated on the Primary block.
    The Primary block should only be used to render the primary story, which is automatically found.
    `),story?React17.createElement(DocsStory,{of:story.moduleExport,expanded:!1,__primary:!0,withToolbar:!0}):null};var Heading2=({children,disableAnchor,...props})=>{if(disableAnchor||typeof children!="string")return React17.createElement(H2,null,children);let tagID=children.toLowerCase().replace(/[^a-z0-9]/gi,"-");return React17.createElement(HeaderMdx,{as:"h2",id:tagID,...props},children)};var StyledHeading=styled(Heading2)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,fontWeight:theme.typography.weight.bold,lineHeight:"16px",letterSpacing:"0.35em",textTransform:"uppercase",color:theme.textMutedColor,border:0,marginBottom:"12px","&:first-of-type":{marginTop:"56px"}})),Stories=({title="Stories",includePrimary=!0})=>{let{componentStories}=useContext(DocsContext),stories=componentStories().filter(story=>!story.parameters?.docs?.disable);return includePrimary||(stories=stories.slice(1)),!stories||stories.length===0?null:React17.createElement(React17.Fragment,null,React17.createElement(StyledHeading,null,title),stories.map(story=>story&&React17.createElement(DocsStory,{key:story.id,of:story.moduleExport,expanded:!0,__forceInitialArgs:!0})))};var DocsPage=()=>{let resolvedOf=useOf("meta",["meta"]),{stories}=resolvedOf.csfFile,isSingleStory=Object.keys(stories).length===1;return React17.createElement(React17.Fragment,null,React17.createElement(Title3,null),React17.createElement(Subtitle2,null),React17.createElement(DescriptionContainer,{of:"meta"}),isSingleStory?React17.createElement(DescriptionContainer,{of:"story"}):null,React17.createElement(Primary,null),React17.createElement(Controls3,null),isSingleStory?null:React17.createElement(Stories,null))};function Docs({context,docsParameter}){let Container=docsParameter.container||DocsContainer,Page=docsParameter.page||DocsPage;return React17.createElement(Container,{context,theme:docsParameter.theme},React17.createElement(Page,null))}var ExternalDocsContext=class extends DocsContext$1{constructor(channel,store,renderStoryToElement,processMetaExports){super(channel,store,renderStoryToElement,[]);this.channel=channel;this.store=store;this.renderStoryToElement=renderStoryToElement;this.processMetaExports=processMetaExports;this.referenceMeta=(metaExports,attach)=>{let csfFile=this.processMetaExports(metaExports);this.referenceCSFFile(csfFile),super.referenceMeta(metaExports,attach);};}};var ConstantMap=class{constructor(prefix){this.prefix=prefix;this.entries=new Map;}get(key){return this.entries.has(key)||this.entries.set(key,`${this.prefix}${this.entries.size}`),this.entries.get(key)}},ExternalPreview=class extends Preview$1{constructor(projectAnnotations){super(new Channel({}));this.projectAnnotations=projectAnnotations;this.importPaths=new ConstantMap("./importPath/");this.titles=new ConstantMap("title-");this.storyIndex={v:4,entries:{}};this.moduleExportsByImportPath={};this.processMetaExports=metaExports=>{let importPath=this.importPaths.get(metaExports);this.moduleExportsByImportPath[importPath]=metaExports;let title=metaExports.default.title||this.titles.get(metaExports),csfFile=this.storyStore.processCSFFileWithCache(metaExports,importPath,title);return Object.values(csfFile.stories).forEach(({id,name})=>{this.storyIndex.entries[id]={id,importPath,title,name,type:"story"};}),this.onStoriesChanged({storyIndex:this.storyIndex}),csfFile};this.docsContext=()=>new ExternalDocsContext(this.channel,this.storyStore,this.renderStoryToElement.bind(this),this.processMetaExports.bind(this));this.initialize({getStoryIndex:()=>this.storyIndex,importFn:path=>Promise.resolve(this.moduleExportsByImportPath[path]),getProjectAnnotations:()=>composeConfigs([{parameters:{docs:{story:{inline:!0}}}},this.projectAnnotations])});}};function usePreview(projectAnnotations){let previewRef=useRef();return previewRef.current||(previewRef.current=new ExternalPreview(projectAnnotations)),previewRef.current}function ExternalDocs({projectAnnotationsList,children}){let projectAnnotations=composeConfigs(projectAnnotationsList),preview2=usePreview(projectAnnotations),docsParameter={...projectAnnotations.parameters?.docs,page:()=>children};return React17.createElement(Docs,{docsParameter,context:preview2.docsContext()})}var preview,ExternalDocsContainer=({projectAnnotations,children})=>(preview||(preview=new ExternalPreview(projectAnnotations)),React17.createElement(DocsContext.Provider,{value:preview.docsContext()},React17.createElement(ThemeProvider,{theme:ensure(themes.light)},children)));var Meta=({of})=>{let context=useContext(DocsContext);of&&context.referenceMeta(of,!0);try{let primary=context.storyById();return React17.createElement(Anchor,{storyId:primary.id})}catch{return null}};var Unstyled=props=>React17.createElement("div",{...props,className:"sb-unstyled"});var Wrapper10=({children})=>React17.createElement("div",{style:{fontFamily:"sans-serif"}},children);

export { AddContext, Anchor, AnchorMdx, ArgTypes, ArgsTable2 as ArgsTable, BooleanControl, Canvas, CodeOrSourceMdx, ColorControl, ColorItem, ColorPalette, ComponentsTable, Controls3 as Controls, DateControl, DescriptionContainer as Description, DescriptionType, Docs, DocsContainer, DocsContext, DocsPage, DocsStory, ExternalDocs, ExternalDocsContainer, FilesControl, HeaderMdx, HeadersMdx, Heading2 as Heading, IconGallery, IconItem, Markdown2 as Markdown, Meta, NumberControl, ObjectControl, OptionsControl, PRIMARY_STORY, Primary, ArgsTable as PureArgsTable, RangeControl, Source2 as Source, SourceContainer, SourceContext, SourceState, Stories, Story2 as Story, StoryTable, Subheading, Subtitle2 as Subtitle, TextControl, Title3 as Title, Typeset, UNKNOWN_ARGS_HASH, Unstyled, Wrapper10 as Wrapper, anchorBlockIdFromId, argsHash, assertIsFn, extractComponentArgTypes2 as extractComponentArgTypes, extractTitle, format, formatDate, formatTime, getComponent, getStoryId2 as getStoryId, getStoryProps, parse2 as parse, parseDate, parseTime, useOf, useSourceProps };
