{"name": "@storybook/node-logger", "version": "7.6.20", "description": "", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/node-logger", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/node-logger"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "devDependencies": {"@types/npmlog": "^4.1.2", "@types/pretty-hrtime": "^1.0.0", "chalk": "^4.1.0", "npmlog": "^5.0.1", "pretty-hrtime": "^1.0.3", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"], "formats": ["cjs"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}