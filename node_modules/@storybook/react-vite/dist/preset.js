"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __esm=(fn,res)=>function(){return fn&&(res=(0,fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var import_react_docgen,getNameOrValue,isReactForwardRefCall,actualNameHandler,actualNameHandler_default,init_actualNameHandler=__esm({"src/plugins/docgen-handlers/actualNameHandler.ts"(){"use strict";import_react_docgen=require("react-docgen"),{getNameOrValue,isReactForwardRefCall}=import_react_docgen.utils,actualNameHandler=function(documentation,componentDefinition){if((componentDefinition.isClassDeclaration()||componentDefinition.isFunctionDeclaration())&&componentDefinition.has("id"))documentation.set("actualName",getNameOrValue(componentDefinition.get("id")));else if(componentDefinition.isArrowFunctionExpression()||componentDefinition.isFunctionExpression()||isReactForwardRefCall(componentDefinition)){let currentPath=componentDefinition;for(;currentPath.parentPath;){if(currentPath.parentPath.isVariableDeclarator()){documentation.set("actualName",getNameOrValue(currentPath.parentPath.get("id")));return}if(currentPath.parentPath.isAssignmentExpression()){let leftPath=currentPath.parentPath.get("left");if(leftPath.isIdentifier()||leftPath.isLiteral()){documentation.set("actualName",getNameOrValue(leftPath));return}}currentPath=currentPath.parentPath}documentation.set("actualName","")}},actualNameHandler_default=actualNameHandler}});var react_docgen_exports={};__export(react_docgen_exports,{reactDocgen:()=>reactDocgen});function reactDocgen({include=/\.(mjs|tsx?|jsx?)$/,exclude=[/node_modules\/.*/]}={}){let cwd=process.cwd(),filter=(0,import_pluginutils.createFilter)(include,exclude);return{name:"storybook:react-docgen-plugin",enforce:"pre",async transform(src,id){let relPath=import_path.default.relative(cwd,id);if(filter(relPath))try{let docgenResults=(0,import_react_docgen2.parse)(src,{resolver:defaultResolver,handlers,importer:defaultImporter,filename:id}),s=new import_magic_string.default(src);return docgenResults.forEach(info=>{let{actualName,...docgenInfo}=info;if(actualName){let docNode=JSON.stringify(docgenInfo);s.append(`;${actualName}.__docgenInfo=${docNode}`)}}),{code:s.toString(),map:s.generateMap()}}catch(e){if(e.code===import_react_docgen2.ERROR_CODES.MISSING_DEFINITION)return;throw e}}}}var import_path,import_pluginutils,import_react_docgen2,import_magic_string,defaultHandlers,defaultResolver,defaultImporter,handlers,init_react_docgen=__esm({"src/plugins/react-docgen.ts"(){"use strict";import_path=__toESM(require("path")),import_pluginutils=require("@rollup/pluginutils"),import_react_docgen2=require("react-docgen"),import_magic_string=__toESM(require("magic-string"));init_actualNameHandler();defaultHandlers=Object.values(import_react_docgen2.builtinHandlers).map(handler=>handler),defaultResolver=new import_react_docgen2.builtinResolvers.FindExportedDefinitionsResolver,defaultImporter=import_react_docgen2.builtinImporters.fsImporter,handlers=[...defaultHandlers,actualNameHandler_default]}});var preset_exports={};__export(preset_exports,{core:()=>core,viteFinal:()=>viteFinal});module.exports=__toCommonJS(preset_exports);var import_builder_vite=require("@storybook/builder-vite"),import_path2=require("path"),getAbsolutePath=input=>(0,import_path2.dirname)(require.resolve((0,import_path2.join)(input,"package.json"))),core={builder:getAbsolutePath("@storybook/builder-vite"),renderer:getAbsolutePath("@storybook/react")},viteFinal=async(config,{presets})=>{let{plugins=[]}=config;if(!await(0,import_builder_vite.hasVitePlugins)(plugins,["vite:react-babel","vite:react-swc"])){let{default:react}=await import("@vitejs/plugin-react");plugins.push(react())}let{reactDocgen:reactDocgenOption,reactDocgenTypescriptOptions}=await presets.apply("typescript",{}),typescriptPresent;try{require.resolve("typescript"),typescriptPresent=!0}catch{typescriptPresent=!1}if(reactDocgenOption==="react-docgen-typescript"&&typescriptPresent&&plugins.push(require("@joshwooding/vite-plugin-react-docgen-typescript")({...reactDocgenTypescriptOptions,savePropValueAsString:!0})),typeof reactDocgenOption=="string"){let{reactDocgen:reactDocgen2}=await Promise.resolve().then(()=>(init_react_docgen(),react_docgen_exports));plugins.unshift(reactDocgen2({include:reactDocgenOption==="react-docgen"?/\.(mjs|tsx?|jsx?)$/:/\.(mjs|jsx?)$/}))}return config};0&&(module.exports={core,viteFinal});
