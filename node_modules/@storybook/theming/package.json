{"name": "@storybook/theming", "version": "7.6.20", "description": "Core Storybook Components", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/theming", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/theming"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./create": {"types": "./dist/create.d.ts", "require": "./dist/create.js", "import": "./dist/create.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@emotion/use-insertion-effect-with-fallbacks": "^1.0.0", "@storybook/client-logger": "7.6.20", "@storybook/global": "^5.0.0", "memoizerific": "^1.11.3"}, "devDependencies": {"@emotion/cache": "^11.10.7", "@emotion/is-prop-valid": "^1.2.0", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@types/fs-extra": "^11.0.1", "@types/node": "^18.0.0", "deep-object-diff": "^1.1.0", "fs-extra": "^11.1.0", "polished": "^4.2.2", "ts-dedent": "^2.0.0", "typescript": "~4.9.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts", "./src/create.ts"], "externals": ["react", "react-dom", "@storybook/addons", "@storybook/api", "@storybook/channels", "@storybook/client-logger", "@storybook/components", "@storybook/core-events", "@storybook/manager-api", "@storybook/router", "@storybook/theming", "@storybook/types"], "post": "./scripts/fix-theme-type-export.ts"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}