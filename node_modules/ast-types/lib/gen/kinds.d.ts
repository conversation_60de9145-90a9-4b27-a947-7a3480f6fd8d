import { namedTypes } from "./namedTypes";
export type PrintableKind = namedTypes.File | namedTypes.Program | namedTypes.Identifier | namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.SwitchCase | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.CatchClause | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.VariableDeclarator | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Property | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.RestElement | namedTypes.TypeAnnotation | namedTypes.TSTypeAnnotation | namedTypes.SpreadElementPattern | namedTypes.ArrowFunctionExpression | namedTypes.ForOfStatement | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionBlock | namedTypes.ComprehensionExpression | namedTypes.ObjectProperty | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.SpreadElement | namedTypes.AssignmentPattern | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.StaticBlock | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ClassExpression | namedTypes.Super | namedTypes.ImportSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportSpecifier | namedTypes.ExportDefaultDeclaration | namedTypes.ExportAllDeclaration | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.TemplateElement | namedTypes.MetaProperty | namedTypes.AwaitExpression | namedTypes.SpreadProperty | namedTypes.SpreadPropertyPattern | namedTypes.ImportExpression | namedTypes.ChainExpression | namedTypes.OptionalCallExpression | namedTypes.OptionalMemberExpression | namedTypes.Decorator | namedTypes.PrivateName | namedTypes.ClassPrivateProperty | namedTypes.ImportAttribute | namedTypes.RecordExpression | namedTypes.ObjectMethod | namedTypes.TupleExpression | namedTypes.ModuleExpression | namedTypes.JSXAttribute | namedTypes.JSXIdentifier | namedTypes.JSXNamespacedName | namedTypes.JSXExpressionContainer | namedTypes.JSXElement | namedTypes.JSXFragment | namedTypes.JSXMemberExpression | namedTypes.JSXSpreadAttribute | namedTypes.JSXEmptyExpression | namedTypes.JSXText | namedTypes.JSXSpreadChild | namedTypes.JSXOpeningElement | namedTypes.JSXClosingElement | namedTypes.JSXOpeningFragment | namedTypes.JSXClosingFragment | namedTypes.TypeParameterDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.TypeParameterInstantiation | namedTypes.TSTypeParameterInstantiation | namedTypes.ClassImplements | namedTypes.TSExpressionWithTypeArguments | namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.SymbolTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.BigIntTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.BigIntLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.FunctionTypeParam | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.ObjectTypeProperty | namedTypes.ObjectTypeSpreadProperty | namedTypes.ObjectTypeIndexer | namedTypes.ObjectTypeCallProperty | namedTypes.ObjectTypeInternalSlot | namedTypes.Variance | namedTypes.QualifiedTypeIdentifier | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.IndexedAccessType | namedTypes.OptionalIndexedAccessType | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.InterfaceExtends | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.DeclareTypeAlias | namedTypes.OpaqueType | namedTypes.DeclareOpaqueType | namedTypes.TypeCastExpression | namedTypes.TupleTypeAnnotation | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.ExportBatchSpecifier | namedTypes.DeclareExportAllDeclaration | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate | namedTypes.EnumDeclaration | namedTypes.ExportDeclaration | namedTypes.Block | namedTypes.Line | namedTypes.Noop | namedTypes.DoExpression | namedTypes.BindExpression | namedTypes.ParenthesizedExpression | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier | namedTypes.CommentBlock | namedTypes.CommentLine | namedTypes.Directive | namedTypes.DirectiveLiteral | namedTypes.InterpreterDirective | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.DecimalLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.ClassAccessorProperty | namedTypes.RestProperty | namedTypes.ForAwaitStatement | namedTypes.Import | namedTypes.V8IntrinsicIdentifier | namedTypes.TopicReference | namedTypes.TSQualifiedName | namedTypes.TSTypeReference | namedTypes.TSAsExpression | namedTypes.TSTypeCastExpression | namedTypes.TSSatisfiesExpression | namedTypes.TSNonNullExpression | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSIntrinsicKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSTypeParameter | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSTypePredicate | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumMember | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral | namedTypes.TSTypeAssertion | namedTypes.TSInstantiationExpression | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleBlock | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceBody | namedTypes.TSInterfaceDeclaration | namedTypes.TSParameterProperty;
export type SourceLocationKind = namedTypes.SourceLocation;
export type NodeKind = namedTypes.File | namedTypes.Program | namedTypes.Identifier | namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.SwitchCase | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.CatchClause | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.VariableDeclarator | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Property | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.RestElement | namedTypes.TypeAnnotation | namedTypes.TSTypeAnnotation | namedTypes.SpreadElementPattern | namedTypes.ArrowFunctionExpression | namedTypes.ForOfStatement | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionBlock | namedTypes.ComprehensionExpression | namedTypes.ObjectProperty | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.SpreadElement | namedTypes.AssignmentPattern | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.StaticBlock | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ClassExpression | namedTypes.Super | namedTypes.ImportSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ImportDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportSpecifier | namedTypes.ExportDefaultDeclaration | namedTypes.ExportAllDeclaration | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.TemplateElement | namedTypes.MetaProperty | namedTypes.AwaitExpression | namedTypes.SpreadProperty | namedTypes.SpreadPropertyPattern | namedTypes.ImportExpression | namedTypes.ChainExpression | namedTypes.OptionalCallExpression | namedTypes.OptionalMemberExpression | namedTypes.Decorator | namedTypes.PrivateName | namedTypes.ClassPrivateProperty | namedTypes.ImportAttribute | namedTypes.RecordExpression | namedTypes.ObjectMethod | namedTypes.TupleExpression | namedTypes.ModuleExpression | namedTypes.JSXAttribute | namedTypes.JSXIdentifier | namedTypes.JSXNamespacedName | namedTypes.JSXExpressionContainer | namedTypes.JSXElement | namedTypes.JSXFragment | namedTypes.JSXMemberExpression | namedTypes.JSXSpreadAttribute | namedTypes.JSXEmptyExpression | namedTypes.JSXText | namedTypes.JSXSpreadChild | namedTypes.JSXOpeningElement | namedTypes.JSXClosingElement | namedTypes.JSXOpeningFragment | namedTypes.JSXClosingFragment | namedTypes.TypeParameterDeclaration | namedTypes.TSTypeParameterDeclaration | namedTypes.TypeParameterInstantiation | namedTypes.TSTypeParameterInstantiation | namedTypes.ClassImplements | namedTypes.TSExpressionWithTypeArguments | namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.SymbolTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.BigIntTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.BigIntLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.FunctionTypeParam | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.ObjectTypeProperty | namedTypes.ObjectTypeSpreadProperty | namedTypes.ObjectTypeIndexer | namedTypes.ObjectTypeCallProperty | namedTypes.ObjectTypeInternalSlot | namedTypes.Variance | namedTypes.QualifiedTypeIdentifier | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.IndexedAccessType | namedTypes.OptionalIndexedAccessType | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.InterfaceExtends | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.DeclareTypeAlias | namedTypes.OpaqueType | namedTypes.DeclareOpaqueType | namedTypes.TypeCastExpression | namedTypes.TupleTypeAnnotation | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.ExportBatchSpecifier | namedTypes.DeclareExportAllDeclaration | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate | namedTypes.EnumDeclaration | namedTypes.ExportDeclaration | namedTypes.Noop | namedTypes.DoExpression | namedTypes.BindExpression | namedTypes.ParenthesizedExpression | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier | namedTypes.Directive | namedTypes.DirectiveLiteral | namedTypes.InterpreterDirective | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.DecimalLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.ClassAccessorProperty | namedTypes.RestProperty | namedTypes.ForAwaitStatement | namedTypes.Import | namedTypes.V8IntrinsicIdentifier | namedTypes.TopicReference | namedTypes.TSQualifiedName | namedTypes.TSTypeReference | namedTypes.TSAsExpression | namedTypes.TSTypeCastExpression | namedTypes.TSSatisfiesExpression | namedTypes.TSNonNullExpression | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSIntrinsicKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSTypeParameter | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSTypePredicate | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumMember | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral | namedTypes.TSTypeAssertion | namedTypes.TSInstantiationExpression | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleBlock | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceBody | namedTypes.TSInterfaceDeclaration | namedTypes.TSParameterProperty;
export type CommentKind = namedTypes.Block | namedTypes.Line | namedTypes.CommentBlock | namedTypes.CommentLine;
export type PositionKind = namedTypes.Position;
export type FileKind = namedTypes.File;
export type ProgramKind = namedTypes.Program;
export type StatementKind = namedTypes.BlockStatement | namedTypes.EmptyStatement | namedTypes.ExpressionStatement | namedTypes.IfStatement | namedTypes.LabeledStatement | namedTypes.BreakStatement | namedTypes.ContinueStatement | namedTypes.WithStatement | namedTypes.SwitchStatement | namedTypes.ReturnStatement | namedTypes.ThrowStatement | namedTypes.TryStatement | namedTypes.WhileStatement | namedTypes.DoWhileStatement | namedTypes.ForStatement | namedTypes.VariableDeclaration | namedTypes.ForInStatement | namedTypes.DebuggerStatement | namedTypes.FunctionDeclaration | namedTypes.ForOfStatement | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.StaticBlock | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ImportDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportDefaultDeclaration | namedTypes.ExportAllDeclaration | namedTypes.ClassPrivateProperty | namedTypes.TSTypeParameterDeclaration | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.DeclareTypeAlias | namedTypes.OpaqueType | namedTypes.DeclareOpaqueType | namedTypes.DeclareVariable | namedTypes.DeclareFunction | namedTypes.DeclareClass | namedTypes.DeclareModule | namedTypes.DeclareModuleExports | namedTypes.DeclareExportDeclaration | namedTypes.DeclareExportAllDeclaration | namedTypes.EnumDeclaration | namedTypes.ExportDeclaration | namedTypes.Noop | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.ClassAccessorProperty | namedTypes.ForAwaitStatement | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSExportAssignment | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceDeclaration;
export type FunctionKind = namedTypes.FunctionDeclaration | namedTypes.FunctionExpression | namedTypes.ArrowFunctionExpression | namedTypes.ObjectMethod | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod;
export type ExpressionKind = namedTypes.Identifier | namedTypes.FunctionExpression | namedTypes.ThisExpression | namedTypes.ArrayExpression | namedTypes.ObjectExpression | namedTypes.Literal | namedTypes.SequenceExpression | namedTypes.UnaryExpression | namedTypes.BinaryExpression | namedTypes.AssignmentExpression | namedTypes.MemberExpression | namedTypes.UpdateExpression | namedTypes.LogicalExpression | namedTypes.ConditionalExpression | namedTypes.NewExpression | namedTypes.CallExpression | namedTypes.ArrowFunctionExpression | namedTypes.YieldExpression | namedTypes.GeneratorExpression | namedTypes.ComprehensionExpression | namedTypes.ClassExpression | namedTypes.Super | namedTypes.TaggedTemplateExpression | namedTypes.TemplateLiteral | namedTypes.MetaProperty | namedTypes.AwaitExpression | namedTypes.ImportExpression | namedTypes.ChainExpression | namedTypes.OptionalCallExpression | namedTypes.OptionalMemberExpression | namedTypes.PrivateName | namedTypes.RecordExpression | namedTypes.TupleExpression | namedTypes.JSXIdentifier | namedTypes.JSXExpressionContainer | namedTypes.JSXElement | namedTypes.JSXFragment | namedTypes.JSXMemberExpression | namedTypes.JSXText | namedTypes.TypeCastExpression | namedTypes.DoExpression | namedTypes.BindExpression | namedTypes.ParenthesizedExpression | namedTypes.DirectiveLiteral | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.DecimalLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral | namedTypes.Import | namedTypes.V8IntrinsicIdentifier | namedTypes.TopicReference | namedTypes.TSAsExpression | namedTypes.TSTypeCastExpression | namedTypes.TSSatisfiesExpression | namedTypes.TSNonNullExpression | namedTypes.TSTypeParameter | namedTypes.TSTypeAssertion | namedTypes.TSInstantiationExpression;
export type PatternKind = namedTypes.Identifier | namedTypes.RestElement | namedTypes.SpreadElementPattern | namedTypes.PropertyPattern | namedTypes.ObjectPattern | namedTypes.ArrayPattern | namedTypes.AssignmentPattern | namedTypes.SpreadPropertyPattern | namedTypes.PrivateName | namedTypes.JSXIdentifier | namedTypes.TSAsExpression | namedTypes.TSSatisfiesExpression | namedTypes.TSNonNullExpression | namedTypes.TSTypeParameter | namedTypes.TSTypeAssertion | namedTypes.TSParameterProperty;
export type IdentifierKind = namedTypes.Identifier | namedTypes.JSXIdentifier | namedTypes.TSTypeParameter;
export type BlockStatementKind = namedTypes.BlockStatement;
export type EmptyStatementKind = namedTypes.EmptyStatement;
export type ExpressionStatementKind = namedTypes.ExpressionStatement;
export type IfStatementKind = namedTypes.IfStatement;
export type LabeledStatementKind = namedTypes.LabeledStatement;
export type BreakStatementKind = namedTypes.BreakStatement;
export type ContinueStatementKind = namedTypes.ContinueStatement;
export type WithStatementKind = namedTypes.WithStatement;
export type SwitchStatementKind = namedTypes.SwitchStatement;
export type SwitchCaseKind = namedTypes.SwitchCase;
export type ReturnStatementKind = namedTypes.ReturnStatement;
export type ThrowStatementKind = namedTypes.ThrowStatement;
export type TryStatementKind = namedTypes.TryStatement;
export type CatchClauseKind = namedTypes.CatchClause;
export type WhileStatementKind = namedTypes.WhileStatement;
export type DoWhileStatementKind = namedTypes.DoWhileStatement;
export type ForStatementKind = namedTypes.ForStatement;
export type DeclarationKind = namedTypes.VariableDeclaration | namedTypes.FunctionDeclaration | namedTypes.MethodDefinition | namedTypes.ClassPropertyDefinition | namedTypes.ClassProperty | namedTypes.StaticBlock | namedTypes.ClassBody | namedTypes.ClassDeclaration | namedTypes.ImportDeclaration | namedTypes.ExportNamedDeclaration | namedTypes.ExportDefaultDeclaration | namedTypes.ExportAllDeclaration | namedTypes.ClassPrivateProperty | namedTypes.TSTypeParameterDeclaration | namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.TypeAlias | namedTypes.DeclareTypeAlias | namedTypes.OpaqueType | namedTypes.DeclareOpaqueType | namedTypes.DeclareClass | namedTypes.DeclareExportDeclaration | namedTypes.DeclareExportAllDeclaration | namedTypes.EnumDeclaration | namedTypes.ExportDeclaration | namedTypes.ClassMethod | namedTypes.ClassPrivateMethod | namedTypes.ClassAccessorProperty | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSEnumDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSModuleDeclaration | namedTypes.TSImportEqualsDeclaration | namedTypes.TSExternalModuleReference | namedTypes.TSNamespaceExportDeclaration | namedTypes.TSInterfaceDeclaration;
export type VariableDeclarationKind = namedTypes.VariableDeclaration;
export type ForInStatementKind = namedTypes.ForInStatement;
export type DebuggerStatementKind = namedTypes.DebuggerStatement;
export type FunctionDeclarationKind = namedTypes.FunctionDeclaration;
export type FunctionExpressionKind = namedTypes.FunctionExpression;
export type VariableDeclaratorKind = namedTypes.VariableDeclarator;
export type ThisExpressionKind = namedTypes.ThisExpression;
export type ArrayExpressionKind = namedTypes.ArrayExpression;
export type ObjectExpressionKind = namedTypes.ObjectExpression;
export type PropertyKind = namedTypes.Property;
export type LiteralKind = namedTypes.Literal | namedTypes.JSXText | namedTypes.StringLiteral | namedTypes.NumericLiteral | namedTypes.BigIntLiteral | namedTypes.DecimalLiteral | namedTypes.NullLiteral | namedTypes.BooleanLiteral | namedTypes.RegExpLiteral;
export type SequenceExpressionKind = namedTypes.SequenceExpression;
export type UnaryExpressionKind = namedTypes.UnaryExpression;
export type BinaryExpressionKind = namedTypes.BinaryExpression;
export type AssignmentExpressionKind = namedTypes.AssignmentExpression;
export type ChainElementKind = namedTypes.MemberExpression | namedTypes.CallExpression | namedTypes.OptionalCallExpression | namedTypes.OptionalMemberExpression | namedTypes.JSXMemberExpression;
export type MemberExpressionKind = namedTypes.MemberExpression | namedTypes.OptionalMemberExpression | namedTypes.JSXMemberExpression;
export type UpdateExpressionKind = namedTypes.UpdateExpression;
export type LogicalExpressionKind = namedTypes.LogicalExpression;
export type ConditionalExpressionKind = namedTypes.ConditionalExpression;
export type NewExpressionKind = namedTypes.NewExpression;
export type CallExpressionKind = namedTypes.CallExpression | namedTypes.OptionalCallExpression;
export type RestElementKind = namedTypes.RestElement;
export type TypeAnnotationKind = namedTypes.TypeAnnotation;
export type TSTypeAnnotationKind = namedTypes.TSTypeAnnotation | namedTypes.TSTypePredicate;
export type SpreadElementPatternKind = namedTypes.SpreadElementPattern;
export type ArrowFunctionExpressionKind = namedTypes.ArrowFunctionExpression;
export type ForOfStatementKind = namedTypes.ForOfStatement;
export type YieldExpressionKind = namedTypes.YieldExpression;
export type GeneratorExpressionKind = namedTypes.GeneratorExpression;
export type ComprehensionBlockKind = namedTypes.ComprehensionBlock;
export type ComprehensionExpressionKind = namedTypes.ComprehensionExpression;
export type ObjectPropertyKind = namedTypes.ObjectProperty;
export type PropertyPatternKind = namedTypes.PropertyPattern;
export type ObjectPatternKind = namedTypes.ObjectPattern;
export type ArrayPatternKind = namedTypes.ArrayPattern;
export type SpreadElementKind = namedTypes.SpreadElement;
export type AssignmentPatternKind = namedTypes.AssignmentPattern;
export type MethodDefinitionKind = namedTypes.MethodDefinition;
export type ClassPropertyDefinitionKind = namedTypes.ClassPropertyDefinition;
export type ClassPropertyKind = namedTypes.ClassProperty | namedTypes.ClassPrivateProperty;
export type StaticBlockKind = namedTypes.StaticBlock;
export type ClassBodyKind = namedTypes.ClassBody;
export type ClassDeclarationKind = namedTypes.ClassDeclaration;
export type ClassExpressionKind = namedTypes.ClassExpression;
export type SuperKind = namedTypes.Super;
export type SpecifierKind = namedTypes.ImportSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ExportSpecifier | namedTypes.ExportBatchSpecifier | namedTypes.ExportNamespaceSpecifier | namedTypes.ExportDefaultSpecifier;
export type ModuleSpecifierKind = namedTypes.ImportSpecifier | namedTypes.ImportDefaultSpecifier | namedTypes.ImportNamespaceSpecifier | namedTypes.ExportSpecifier;
export type ImportSpecifierKind = namedTypes.ImportSpecifier;
export type ImportDefaultSpecifierKind = namedTypes.ImportDefaultSpecifier;
export type ImportNamespaceSpecifierKind = namedTypes.ImportNamespaceSpecifier;
export type ImportDeclarationKind = namedTypes.ImportDeclaration;
export type ExportNamedDeclarationKind = namedTypes.ExportNamedDeclaration;
export type ExportSpecifierKind = namedTypes.ExportSpecifier;
export type ExportDefaultDeclarationKind = namedTypes.ExportDefaultDeclaration;
export type ExportAllDeclarationKind = namedTypes.ExportAllDeclaration;
export type TaggedTemplateExpressionKind = namedTypes.TaggedTemplateExpression;
export type TemplateLiteralKind = namedTypes.TemplateLiteral;
export type TemplateElementKind = namedTypes.TemplateElement;
export type MetaPropertyKind = namedTypes.MetaProperty;
export type AwaitExpressionKind = namedTypes.AwaitExpression;
export type SpreadPropertyKind = namedTypes.SpreadProperty;
export type SpreadPropertyPatternKind = namedTypes.SpreadPropertyPattern;
export type ImportExpressionKind = namedTypes.ImportExpression;
export type ChainExpressionKind = namedTypes.ChainExpression;
export type OptionalCallExpressionKind = namedTypes.OptionalCallExpression;
export type OptionalMemberExpressionKind = namedTypes.OptionalMemberExpression;
export type DecoratorKind = namedTypes.Decorator;
export type PrivateNameKind = namedTypes.PrivateName;
export type ClassPrivatePropertyKind = namedTypes.ClassPrivateProperty;
export type ImportAttributeKind = namedTypes.ImportAttribute;
export type RecordExpressionKind = namedTypes.RecordExpression;
export type ObjectMethodKind = namedTypes.ObjectMethod;
export type TupleExpressionKind = namedTypes.TupleExpression;
export type ModuleExpressionKind = namedTypes.ModuleExpression;
export type JSXAttributeKind = namedTypes.JSXAttribute;
export type JSXIdentifierKind = namedTypes.JSXIdentifier;
export type JSXNamespacedNameKind = namedTypes.JSXNamespacedName;
export type JSXExpressionContainerKind = namedTypes.JSXExpressionContainer;
export type JSXElementKind = namedTypes.JSXElement;
export type JSXFragmentKind = namedTypes.JSXFragment;
export type JSXMemberExpressionKind = namedTypes.JSXMemberExpression;
export type JSXSpreadAttributeKind = namedTypes.JSXSpreadAttribute;
export type JSXEmptyExpressionKind = namedTypes.JSXEmptyExpression;
export type JSXTextKind = namedTypes.JSXText;
export type JSXSpreadChildKind = namedTypes.JSXSpreadChild;
export type JSXOpeningElementKind = namedTypes.JSXOpeningElement;
export type JSXClosingElementKind = namedTypes.JSXClosingElement;
export type JSXOpeningFragmentKind = namedTypes.JSXOpeningFragment;
export type JSXClosingFragmentKind = namedTypes.JSXClosingFragment;
export type TypeParameterDeclarationKind = namedTypes.TypeParameterDeclaration;
export type TSTypeParameterDeclarationKind = namedTypes.TSTypeParameterDeclaration;
export type TypeParameterInstantiationKind = namedTypes.TypeParameterInstantiation;
export type TSTypeParameterInstantiationKind = namedTypes.TSTypeParameterInstantiation;
export type ClassImplementsKind = namedTypes.ClassImplements;
export type TSTypeKind = namedTypes.TSExpressionWithTypeArguments | namedTypes.TSTypeReference | namedTypes.TSAnyKeyword | namedTypes.TSBigIntKeyword | namedTypes.TSBooleanKeyword | namedTypes.TSNeverKeyword | namedTypes.TSNullKeyword | namedTypes.TSNumberKeyword | namedTypes.TSObjectKeyword | namedTypes.TSStringKeyword | namedTypes.TSSymbolKeyword | namedTypes.TSUndefinedKeyword | namedTypes.TSUnknownKeyword | namedTypes.TSVoidKeyword | namedTypes.TSIntrinsicKeyword | namedTypes.TSThisType | namedTypes.TSArrayType | namedTypes.TSLiteralType | namedTypes.TSUnionType | namedTypes.TSIntersectionType | namedTypes.TSConditionalType | namedTypes.TSInferType | namedTypes.TSParenthesizedType | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSMappedType | namedTypes.TSTupleType | namedTypes.TSNamedTupleMember | namedTypes.TSRestType | namedTypes.TSOptionalType | namedTypes.TSIndexedAccessType | namedTypes.TSTypeOperator | namedTypes.TSTypePredicate | namedTypes.TSTypeQuery | namedTypes.TSImportType | namedTypes.TSTypeLiteral;
export type TSHasOptionalTypeParameterInstantiationKind = namedTypes.TSExpressionWithTypeArguments | namedTypes.TSTypeReference | namedTypes.TSImportType | namedTypes.TSInstantiationExpression;
export type TSExpressionWithTypeArgumentsKind = namedTypes.TSExpressionWithTypeArguments;
export type FlowKind = namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.SymbolTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.BigIntTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.BigIntLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.IndexedAccessType | namedTypes.OptionalIndexedAccessType | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.TupleTypeAnnotation | namedTypes.InferredPredicate | namedTypes.DeclaredPredicate;
export type FlowTypeKind = namedTypes.AnyTypeAnnotation | namedTypes.EmptyTypeAnnotation | namedTypes.MixedTypeAnnotation | namedTypes.VoidTypeAnnotation | namedTypes.SymbolTypeAnnotation | namedTypes.NumberTypeAnnotation | namedTypes.BigIntTypeAnnotation | namedTypes.NumberLiteralTypeAnnotation | namedTypes.NumericLiteralTypeAnnotation | namedTypes.BigIntLiteralTypeAnnotation | namedTypes.StringTypeAnnotation | namedTypes.StringLiteralTypeAnnotation | namedTypes.BooleanTypeAnnotation | namedTypes.BooleanLiteralTypeAnnotation | namedTypes.NullableTypeAnnotation | namedTypes.NullLiteralTypeAnnotation | namedTypes.NullTypeAnnotation | namedTypes.ThisTypeAnnotation | namedTypes.ExistsTypeAnnotation | namedTypes.ExistentialTypeParam | namedTypes.FunctionTypeAnnotation | namedTypes.ArrayTypeAnnotation | namedTypes.ObjectTypeAnnotation | namedTypes.GenericTypeAnnotation | namedTypes.MemberTypeAnnotation | namedTypes.IndexedAccessType | namedTypes.OptionalIndexedAccessType | namedTypes.UnionTypeAnnotation | namedTypes.IntersectionTypeAnnotation | namedTypes.TypeofTypeAnnotation | namedTypes.TypeParameter | namedTypes.InterfaceTypeAnnotation | namedTypes.TupleTypeAnnotation;
export type AnyTypeAnnotationKind = namedTypes.AnyTypeAnnotation;
export type EmptyTypeAnnotationKind = namedTypes.EmptyTypeAnnotation;
export type MixedTypeAnnotationKind = namedTypes.MixedTypeAnnotation;
export type VoidTypeAnnotationKind = namedTypes.VoidTypeAnnotation;
export type SymbolTypeAnnotationKind = namedTypes.SymbolTypeAnnotation;
export type NumberTypeAnnotationKind = namedTypes.NumberTypeAnnotation;
export type BigIntTypeAnnotationKind = namedTypes.BigIntTypeAnnotation;
export type NumberLiteralTypeAnnotationKind = namedTypes.NumberLiteralTypeAnnotation;
export type NumericLiteralTypeAnnotationKind = namedTypes.NumericLiteralTypeAnnotation;
export type BigIntLiteralTypeAnnotationKind = namedTypes.BigIntLiteralTypeAnnotation;
export type StringTypeAnnotationKind = namedTypes.StringTypeAnnotation;
export type StringLiteralTypeAnnotationKind = namedTypes.StringLiteralTypeAnnotation;
export type BooleanTypeAnnotationKind = namedTypes.BooleanTypeAnnotation;
export type BooleanLiteralTypeAnnotationKind = namedTypes.BooleanLiteralTypeAnnotation;
export type NullableTypeAnnotationKind = namedTypes.NullableTypeAnnotation;
export type NullLiteralTypeAnnotationKind = namedTypes.NullLiteralTypeAnnotation;
export type NullTypeAnnotationKind = namedTypes.NullTypeAnnotation;
export type ThisTypeAnnotationKind = namedTypes.ThisTypeAnnotation;
export type ExistsTypeAnnotationKind = namedTypes.ExistsTypeAnnotation;
export type ExistentialTypeParamKind = namedTypes.ExistentialTypeParam;
export type FunctionTypeAnnotationKind = namedTypes.FunctionTypeAnnotation;
export type FunctionTypeParamKind = namedTypes.FunctionTypeParam;
export type ArrayTypeAnnotationKind = namedTypes.ArrayTypeAnnotation;
export type ObjectTypeAnnotationKind = namedTypes.ObjectTypeAnnotation;
export type ObjectTypePropertyKind = namedTypes.ObjectTypeProperty;
export type ObjectTypeSpreadPropertyKind = namedTypes.ObjectTypeSpreadProperty;
export type ObjectTypeIndexerKind = namedTypes.ObjectTypeIndexer;
export type ObjectTypeCallPropertyKind = namedTypes.ObjectTypeCallProperty;
export type ObjectTypeInternalSlotKind = namedTypes.ObjectTypeInternalSlot;
export type VarianceKind = namedTypes.Variance;
export type QualifiedTypeIdentifierKind = namedTypes.QualifiedTypeIdentifier;
export type GenericTypeAnnotationKind = namedTypes.GenericTypeAnnotation;
export type MemberTypeAnnotationKind = namedTypes.MemberTypeAnnotation;
export type IndexedAccessTypeKind = namedTypes.IndexedAccessType;
export type OptionalIndexedAccessTypeKind = namedTypes.OptionalIndexedAccessType;
export type UnionTypeAnnotationKind = namedTypes.UnionTypeAnnotation;
export type IntersectionTypeAnnotationKind = namedTypes.IntersectionTypeAnnotation;
export type TypeofTypeAnnotationKind = namedTypes.TypeofTypeAnnotation;
export type TypeParameterKind = namedTypes.TypeParameter;
export type InterfaceTypeAnnotationKind = namedTypes.InterfaceTypeAnnotation;
export type InterfaceExtendsKind = namedTypes.InterfaceExtends;
export type InterfaceDeclarationKind = namedTypes.InterfaceDeclaration | namedTypes.DeclareInterface | namedTypes.DeclareClass;
export type DeclareInterfaceKind = namedTypes.DeclareInterface;
export type TypeAliasKind = namedTypes.TypeAlias | namedTypes.DeclareTypeAlias;
export type DeclareTypeAliasKind = namedTypes.DeclareTypeAlias;
export type OpaqueTypeKind = namedTypes.OpaqueType | namedTypes.DeclareOpaqueType;
export type DeclareOpaqueTypeKind = namedTypes.DeclareOpaqueType;
export type TypeCastExpressionKind = namedTypes.TypeCastExpression;
export type TupleTypeAnnotationKind = namedTypes.TupleTypeAnnotation;
export type DeclareVariableKind = namedTypes.DeclareVariable;
export type DeclareFunctionKind = namedTypes.DeclareFunction;
export type FlowPredicateKind = namedTypes.InferredPredicate | namedTypes.DeclaredPredicate;
export type DeclareClassKind = namedTypes.DeclareClass;
export type DeclareModuleKind = namedTypes.DeclareModule;
export type DeclareModuleExportsKind = namedTypes.DeclareModuleExports;
export type DeclareExportDeclarationKind = namedTypes.DeclareExportDeclaration;
export type ExportBatchSpecifierKind = namedTypes.ExportBatchSpecifier;
export type DeclareExportAllDeclarationKind = namedTypes.DeclareExportAllDeclaration;
export type InferredPredicateKind = namedTypes.InferredPredicate;
export type DeclaredPredicateKind = namedTypes.DeclaredPredicate;
export type EnumDeclarationKind = namedTypes.EnumDeclaration;
export type EnumBooleanBodyKind = namedTypes.EnumBooleanBody;
export type EnumNumberBodyKind = namedTypes.EnumNumberBody;
export type EnumStringBodyKind = namedTypes.EnumStringBody;
export type EnumSymbolBodyKind = namedTypes.EnumSymbolBody;
export type EnumBooleanMemberKind = namedTypes.EnumBooleanMember;
export type EnumNumberMemberKind = namedTypes.EnumNumberMember;
export type EnumStringMemberKind = namedTypes.EnumStringMember;
export type EnumDefaultedMemberKind = namedTypes.EnumDefaultedMember;
export type ExportDeclarationKind = namedTypes.ExportDeclaration;
export type BlockKind = namedTypes.Block;
export type LineKind = namedTypes.Line;
export type NoopKind = namedTypes.Noop;
export type DoExpressionKind = namedTypes.DoExpression;
export type BindExpressionKind = namedTypes.BindExpression;
export type ParenthesizedExpressionKind = namedTypes.ParenthesizedExpression;
export type ExportNamespaceSpecifierKind = namedTypes.ExportNamespaceSpecifier;
export type ExportDefaultSpecifierKind = namedTypes.ExportDefaultSpecifier;
export type CommentBlockKind = namedTypes.CommentBlock;
export type CommentLineKind = namedTypes.CommentLine;
export type DirectiveKind = namedTypes.Directive;
export type DirectiveLiteralKind = namedTypes.DirectiveLiteral;
export type InterpreterDirectiveKind = namedTypes.InterpreterDirective;
export type StringLiteralKind = namedTypes.StringLiteral;
export type NumericLiteralKind = namedTypes.NumericLiteral;
export type BigIntLiteralKind = namedTypes.BigIntLiteral;
export type DecimalLiteralKind = namedTypes.DecimalLiteral;
export type NullLiteralKind = namedTypes.NullLiteral;
export type BooleanLiteralKind = namedTypes.BooleanLiteral;
export type RegExpLiteralKind = namedTypes.RegExpLiteral;
export type ClassMethodKind = namedTypes.ClassMethod;
export type ClassPrivateMethodKind = namedTypes.ClassPrivateMethod;
export type TSHasOptionalTypeAnnotationKind = namedTypes.ClassAccessorProperty | namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSIndexSignature | namedTypes.TSPropertySignature | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration;
export type ClassAccessorPropertyKind = namedTypes.ClassAccessorProperty;
export type RestPropertyKind = namedTypes.RestProperty;
export type ForAwaitStatementKind = namedTypes.ForAwaitStatement;
export type ImportKind = namedTypes.Import;
export type V8IntrinsicIdentifierKind = namedTypes.V8IntrinsicIdentifier;
export type TopicReferenceKind = namedTypes.TopicReference;
export type TSQualifiedNameKind = namedTypes.TSQualifiedName;
export type TSTypeReferenceKind = namedTypes.TSTypeReference;
export type TSHasOptionalTypeParametersKind = namedTypes.TSFunctionType | namedTypes.TSConstructorType | namedTypes.TSDeclareFunction | namedTypes.TSDeclareMethod | namedTypes.TSMethodSignature | namedTypes.TSCallSignatureDeclaration | namedTypes.TSConstructSignatureDeclaration | namedTypes.TSTypeAliasDeclaration | namedTypes.TSInterfaceDeclaration;
export type TSAsExpressionKind = namedTypes.TSAsExpression;
export type TSTypeCastExpressionKind = namedTypes.TSTypeCastExpression;
export type TSSatisfiesExpressionKind = namedTypes.TSSatisfiesExpression;
export type TSNonNullExpressionKind = namedTypes.TSNonNullExpression;
export type TSAnyKeywordKind = namedTypes.TSAnyKeyword;
export type TSBigIntKeywordKind = namedTypes.TSBigIntKeyword;
export type TSBooleanKeywordKind = namedTypes.TSBooleanKeyword;
export type TSNeverKeywordKind = namedTypes.TSNeverKeyword;
export type TSNullKeywordKind = namedTypes.TSNullKeyword;
export type TSNumberKeywordKind = namedTypes.TSNumberKeyword;
export type TSObjectKeywordKind = namedTypes.TSObjectKeyword;
export type TSStringKeywordKind = namedTypes.TSStringKeyword;
export type TSSymbolKeywordKind = namedTypes.TSSymbolKeyword;
export type TSUndefinedKeywordKind = namedTypes.TSUndefinedKeyword;
export type TSUnknownKeywordKind = namedTypes.TSUnknownKeyword;
export type TSVoidKeywordKind = namedTypes.TSVoidKeyword;
export type TSIntrinsicKeywordKind = namedTypes.TSIntrinsicKeyword;
export type TSThisTypeKind = namedTypes.TSThisType;
export type TSArrayTypeKind = namedTypes.TSArrayType;
export type TSLiteralTypeKind = namedTypes.TSLiteralType;
export type TSUnionTypeKind = namedTypes.TSUnionType;
export type TSIntersectionTypeKind = namedTypes.TSIntersectionType;
export type TSConditionalTypeKind = namedTypes.TSConditionalType;
export type TSInferTypeKind = namedTypes.TSInferType;
export type TSTypeParameterKind = namedTypes.TSTypeParameter;
export type TSParenthesizedTypeKind = namedTypes.TSParenthesizedType;
export type TSFunctionTypeKind = namedTypes.TSFunctionType;
export type TSConstructorTypeKind = namedTypes.TSConstructorType;
export type TSDeclareFunctionKind = namedTypes.TSDeclareFunction;
export type TSDeclareMethodKind = namedTypes.TSDeclareMethod;
export type TSMappedTypeKind = namedTypes.TSMappedType;
export type TSTupleTypeKind = namedTypes.TSTupleType;
export type TSNamedTupleMemberKind = namedTypes.TSNamedTupleMember;
export type TSRestTypeKind = namedTypes.TSRestType;
export type TSOptionalTypeKind = namedTypes.TSOptionalType;
export type TSIndexedAccessTypeKind = namedTypes.TSIndexedAccessType;
export type TSTypeOperatorKind = namedTypes.TSTypeOperator;
export type TSIndexSignatureKind = namedTypes.TSIndexSignature;
export type TSPropertySignatureKind = namedTypes.TSPropertySignature;
export type TSMethodSignatureKind = namedTypes.TSMethodSignature;
export type TSTypePredicateKind = namedTypes.TSTypePredicate;
export type TSCallSignatureDeclarationKind = namedTypes.TSCallSignatureDeclaration;
export type TSConstructSignatureDeclarationKind = namedTypes.TSConstructSignatureDeclaration;
export type TSEnumMemberKind = namedTypes.TSEnumMember;
export type TSTypeQueryKind = namedTypes.TSTypeQuery;
export type TSImportTypeKind = namedTypes.TSImportType;
export type TSTypeLiteralKind = namedTypes.TSTypeLiteral;
export type TSTypeAssertionKind = namedTypes.TSTypeAssertion;
export type TSInstantiationExpressionKind = namedTypes.TSInstantiationExpression;
export type TSEnumDeclarationKind = namedTypes.TSEnumDeclaration;
export type TSTypeAliasDeclarationKind = namedTypes.TSTypeAliasDeclaration;
export type TSModuleBlockKind = namedTypes.TSModuleBlock;
export type TSModuleDeclarationKind = namedTypes.TSModuleDeclaration;
export type TSImportEqualsDeclarationKind = namedTypes.TSImportEqualsDeclaration;
export type TSExternalModuleReferenceKind = namedTypes.TSExternalModuleReference;
export type TSExportAssignmentKind = namedTypes.TSExportAssignment;
export type TSNamespaceExportDeclarationKind = namedTypes.TSNamespaceExportDeclaration;
export type TSInterfaceBodyKind = namedTypes.TSInterfaceBody;
export type TSInterfaceDeclarationKind = namedTypes.TSInterfaceDeclaration;
export type TSParameterPropertyKind = namedTypes.TSParameterProperty;
