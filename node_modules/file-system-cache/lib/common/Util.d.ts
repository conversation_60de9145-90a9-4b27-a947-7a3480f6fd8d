export declare const isNothing: (value: any) => boolean;
export declare const isString: (val: any) => val is string;
export declare const toAbsolutePath: (path: string) => string;
export declare const ensureString: (defaultValue: string, text?: string) => string;
export declare const compact: (input: any[]) => string[];
export declare const toStringArray: (input: any[]) => string[];
export declare const isFileSync: (path: string) => boolean;
export declare const readFileSync: (path: string) => string | undefined;
export declare const filePathsP: (basePath: string, ns: string) => Promise<string[]>;
export declare const hash: (...values: any[]) => string | undefined;
export declare function getValueP(path: string, defaultValue?: any): Promise<any>;
export declare const toGetValue: (data: any) => any;
export declare const toJson: (value: any, ttl: number) => string;
export declare const isExpired: (data: any) => boolean;
