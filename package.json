{"name": "sizewise-suite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@heroicons/react": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/react-vite": "^7.5.3", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.8", "@types/node": "^22.15.29", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "storybook": "^7.5.3", "tailwindcss": "^3.3.6", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "vite": "^5.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "description": "Professional HVAC engineering calculation tools for duct sizing, pressure loss analysis, and system design.", "main": "jest.config.js", "keywords": [], "author": "", "license": "ISC"}