import React, { useId } from 'react';
import { DuctShape } from '../../tools/air-duct-sizer/logic';

interface DuctShapeSelectorProps {
  selectedShape: DuctShape;
  onShapeChange: (shape: DuctShape) => void;
  labelId?: string;
}

export const DuctShapeSelector: React.FC<DuctShapeSelectorProps> = ({
  selectedShape,
  onShapeChange,
  labelId: propLabelId,
}) => {
  const isRectangular = selectedShape === 'rectangular';
  const isRound = selectedShape === 'round';

  const radioGroupId = useId();
  const labelId = propLabelId ?? `duct-shape-label-${radioGroupId}`;
  const rectangularId = `rectangular-${radioGroupId}`;
  const roundId = `round-${radioGroupId}`;

  return (
    <div className="space-y-2">
      <span id={labelId} className="block text-sm font-medium text-gray-700">
        Duct Shape
      </span>
      <div 
        className="flex space-x-4" 
        role="radiogroup" 
        aria-labelledby={labelId}
      >
        <div className="flex items-center">
          <input
            type="radio"
            id={rectangularId}
            name={`duct-shape-${radioGroupId}`}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            checked={isRectangular}
            onChange={() => onShapeChange('rectangular')}
            aria-checked={isRectangular ? "true" : "false"}
          />
          <label 
            htmlFor={rectangularId} 
            className="ml-2 block text-sm text-gray-700 cursor-pointer"
          >
            Rectangular
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id={roundId}
            name={`duct-shape-${radioGroupId}`}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            checked={isRound}
            onChange={() => onShapeChange('round')}
            aria-checked={isRound ? "true" : "false"}
          />
          <label 
            htmlFor={roundId} 
            className="ml-2 block text-sm text-gray-700 cursor-pointer"
          >
            Round
          </label>
        </div>
      </div>
    </div>
  );
};
