import React, { useId } from 'react';
import { Units } from '../../tools/air-duct-sizer/logic';

interface UnitsSelectorProps {
  selectedUnits: Units;
  onUnitsChange: (units: Units) => void;
  labelId?: string;
}

export const UnitsSelector: React.FC<UnitsSelectorProps> = ({
  selectedUnits,
  onUnitsChange,
  labelId: propLabelId,
}) => {

  const radioGroupId = useId();
  const labelId = propLabelId ?? `units-label-${radioGroupId}`;
  const imperialId = `imperial-${radioGroupId}`;
  const metricId = `metric-${radioGroupId}`;

  return (
    <div className="space-y-2">
      <span id={labelId} className="block text-sm font-medium text-gray-700">
        Units
      </span>
      <div 
        className="flex space-x-4" 
        role="radiogroup" 
        aria-labelledby={labelId}
      >
        <div className="flex items-center">
          <input
            type="radio"
            id={imperialId}
            name={`units-${radioGroupId}`}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            checked={selectedUnits === 'imperial'}
            onChange={() => onUnitsChange('imperial')}
            aria-checked={selectedUnits === 'imperial' ? "true" : "false"}
          />
          <label 
            htmlFor={imperialId} 
            className="ml-2 block text-sm text-gray-700 cursor-pointer"
          >
            Imperial (in, ft, F)
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id={metricId}
            name={`units-${radioGroupId}`}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            checked={selectedUnits === 'metric'}
            onChange={() => onUnitsChange('metric')}
            aria-checked={selectedUnits === 'metric' ? "true" : "false"}
          />
          <label 
            htmlFor={metricId} 
            className="ml-2 block text-sm text-gray-700 cursor-pointer"
          >
            Metric (mm, m, C)
          </label>
        </div>
      </div>
    </div>
  );
};
