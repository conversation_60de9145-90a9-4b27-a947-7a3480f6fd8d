import React, { useState } from 'react';
import { Calculator, Info } from 'lucide-react';
import { SMACNAResultsTable, createDuctSizerResults } from '../components/ui/SMACNAResultsTable';

interface LocalDuctInputs {
  cfm: string;
  shape: 'rectangular' | 'circular';
  width: string;
  height: string;
  diameter: string;
  length: string;
  material: 'galvanized' | 'stainless' | 'aluminum';
  application: 'supply' | 'return' | 'exhaust';
}

interface LocalDuctResults {
  velocity: number;
  pressureLoss: number;
  gauge: string;
  jointSpacing: number;
  hangerSpacing: number;
  hydraulicDiameter: number;
  area: number;
  perimeter: number;
  warnings: string[];
  snapSummary: string;
}

const DuctSizerPage: React.FC = () => {
  const [inputs, setInputs] = useState<LocalDuctInputs>({
    cfm: '',
    shape: 'rectangular',
    width: '',
    height: '',
    diameter: '',
    length: '',
    material: 'galvanized',
    application: 'supply',
  });

  const [results, setResults] = useState<LocalDuctResults | null>(null);

  const handleInputChange = (field: keyof LocalDuctInputs, value: string) => {
    setInputs(prev => ({ ...prev, [field]: value }));
  };

  const calculateResults = () => {
    try {
      const cfm = parseFloat(inputs.cfm);
      const length = parseFloat(inputs.length);

      if (!cfm || !length) return;

      // TODO: Implement enhanced calculation logic here using the inputs
      // For now, using simplified calculation
      let area: number;
      let perimeter: number;

      if (inputs.shape === 'rectangular') {
        const width = parseFloat(inputs.width);
        const height = parseFloat(inputs.height);
        if (!width || !height) return;

        area = (width * height) / 144; // Convert to sq ft
        perimeter = (2 * (width + height)) / 12; // Convert to ft
      } else {
        const diameter = parseFloat(inputs.diameter);
        if (!diameter) return;

        area = (Math.PI * Math.pow(diameter, 2)) / (4 * 144); // Convert to sq ft
        perimeter = (Math.PI * diameter) / 12; // Convert to ft
      }

      const velocity = cfm / area; // ft/min
      const hydraulicDiameter = ((4 * area) / perimeter) * 12; // Convert back to inches

      // Simplified pressure loss calculation with material factors
      const materialFactors = {
        galvanized: 0.02,
        stainless: 0.015,
        aluminum: 0.015,
      };

      const frictionFactor = materialFactors[inputs.material];
      /**
       * Calculate the pressure loss using the friction factor, length, velocity, and hydraulic diameter.
       * This uses the Darcy-Weisbach equation, considering friction and duct dimensions.
       * 
       * @param frictionFactor - Coefficient based on duct material and surface roughness
       * @param length - Length of the duct in feet
       * @param velocity - Air velocity in feet per minute
       * @param hydraulicDiameter - Effective duct diameter in inches
       * @returns Pressure loss in inches of water gauge (in. w.g.)
       */
      const pressureLoss =
        (frictionFactor !== undefined &&
          length !== undefined &&
          velocity !== undefined &&
          hydraulicDiameter !== undefined)
          ? (frictionFactor * length * Math.pow(velocity, 2)) / (2 * 4005 * (hydraulicDiameter / 12))
          : 0;

      // Enhanced gauge selection
      let gauge = '26';
      if (pressureLoss > 1) gauge = '24';
      if (pressureLoss > 2) gauge = '22';
      if (pressureLoss > 4) gauge = '20';
      if (pressureLoss > 6) gauge = '18';

      // Enhanced spacing calculations
      const jointSpacing = velocity > 2500 ? 4 : velocity > 2000 ? 6 : 8;
      const hangerSpacing = parseInt(gauge) >= 24 ? 8 : parseInt(gauge) >= 20 ? 10 : 12;

      // Generate warnings
      const warnings: string[] = [];
      if (velocity > 2500)
        warnings.push('Velocity exceeds recommended 2500 ft/min for low-pressure systems');
      if (velocity < 800) warnings.push('Velocity below 800 ft/min may cause stratification');
      if (pressureLoss > 0.1) warnings.push('High pressure loss - consider larger duct size');

      // Generate snap summary
      const shapeDesc =
        inputs.shape === 'rectangular'
          ? `${inputs.width}"×${inputs.height}"`
          : `${inputs.diameter}"⌀`;
      const snapSummary = `${cfm} CFM • ${shapeDesc} • ${Math.round(velocity)} ft/min • ${pressureLoss.toFixed(3)}" w.g. • ${gauge} ga • ${length}' long`;

      setResults({
        velocity: Math.round(velocity),
        pressureLoss: Math.round(pressureLoss * 1000) / 1000,
        console.log('Calculated Velocity:', velocity);
        console.log('Calculated Pressure Loss:', pressureLoss);
        console.log('Selected Gauge:', gauge);
        console.log('Joint Spacing:', jointSpacing);
        console.log('Hanger Spacing:', hangerSpacing);
        console.log('Hydraulic Diameter:', hydraulicDiameter);
        console.log('Area:', area);
        console.log('Perimeter:', perimeter);
        console.log('Warnings:', warnings);
        console.log('Snap Summary:', snapSummary);
        gauge,
        jointSpacing,
        hangerSpacing,
        hydraulicDiameter: Math.round(hydraulicDiameter * 100) / 100,
        area: Math.round(area * 1000) / 1000,
        perimeter: Math.round(perimeter * 100) / 100,
        warnings,
        snapSummary,
      });
    } catch (error) {
      console.error('Calculation error:', error);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Air Duct Sizer</h1>
        <p className="text-gray-600 dark:text-gray-300">
          Calculate duct dimensions, velocity, and pressure loss for HVAC systems
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Panel */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
            <Calculator className="w-5 h-5 mr-2" />
            Input Parameters
          </h2>

          <div className="space-y-6">
            {/* Airflow */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Airflow (CFM)
              </label>
              <input
                type="number"
                value={inputs.cfm}
                onChange={e => handleInputChange('cfm', e.target.value)}
                className="input-field"
                placeholder="Enter CFM"
              />
            </div>

            {/* Duct Shape */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Duct Shape
              </label>
              <select
                aria-label="Select duct shape"
                value={inputs.shape}
                onChange={e =>
                  handleInputChange('shape', e.target.value as 'rectangular' | 'circular')
                }
                className="input-field"
              >
                <option value="rectangular">Rectangular</option>
                <option value="circular">Circular</option>
              </select>
            </div>

            {/* Dimensions */}
            {inputs.shape === 'rectangular' ? (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Width (inches)
                  </label>
                  <input
                    type="number"
                    value={inputs.width}
                    onChange={e => handleInputChange('width', e.target.value)}
                    className="input-field"
                    placeholder="Width"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Height (inches)
                  </label>
                  <input
                    type="number"
                    value={inputs.height}
                    onChange={e => handleInputChange('height', e.target.value)}
                    className="input-field"
                    placeholder="Height"
                  />
                </div>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Diameter (inches)
                </label>
                <input
                  type="number"
                  value={inputs.diameter}
                  onChange={e => handleInputChange('diameter', e.target.value)}
                  className="input-field"
                  placeholder="Diameter"
                />
              </div>
            )}

            {/* Length */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Duct Length (feet)
              </label>
              <input
                type="number"
                value={inputs.length}
                onChange={e => handleInputChange('length', e.target.value)}
                className="input-field"
                placeholder="Length"
              />
            </div>

            {/* Material */}
            <div>
              <label
                htmlFor="material-select"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Material
              </label>
              <select
                id="material-select"
                value={inputs.material}
                onChange={e => handleInputChange('material', e.target.value)}
                className="input-field"
              >
                <option value="galvanized">Galvanized Steel</option>
                <option value="stainless">Stainless Steel</option>
                <option value="aluminum">Aluminum</option>
              </select>
            </div>

            {/* Application */}
            <div>
              <label
                htmlFor="application-select"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Application
              </label>
              <select
                id="application-select"
                value={inputs.application}
                onChange={e => handleInputChange('application', e.target.value)}
                className="input-field"
              >
                <option value="supply">Supply Air</option>
                <option value="return">Return Air</option>
                <option value="exhaust">Exhaust Air</option>
              </select>
            </div>

            <button type="button" onClick={calculateResults} className="btn-primary w-full">
              Calculate
            </button>
          </div>
        </div>

        {/* Modern Results Table */}
        {results ? (
          <SMACNAResultsTable
            title="SizeWise Results: Air Duct Sizing"
            subtitle={`${inputs.material === 'galvanized' ? 'Galvanized Steel' : inputs.material === 'stainless' ? 'Stainless Steel' : 'Aluminum'} • ${inputs.application} air application`}
            results={createDuctSizerResults(
              {
                cfm: parseFloat(inputs.cfm),
                shape: inputs.shape,
                width: inputs.shape === 'rectangular' ? parseFloat(inputs.width) : undefined,
                height: inputs.shape === 'rectangular' ? parseFloat(inputs.height) : undefined,
                diameter: inputs.shape === 'circular' ? parseFloat(inputs.diameter) : undefined,
                length: parseFloat(inputs.length),
                material: inputs.material,
                application: inputs.application,
              },
              {
                velocity: results.velocity,
                pressureLoss: results.pressureLoss,
                gauge: results.gauge,
                jointSpacing: results.jointSpacing,
                hangerSpacing: results.hangerSpacing,
                hydraulicDiameter: results.hydraulicDiameter,
                area: results.area,
              }
            )}
            snapSummary={results.snapSummary}
          />
        ) : (
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              Results
            </h2>
            <div className="text-center text-gray-500 dark:text-gray-400 py-12">
              Enter parameters and click Calculate to see results
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DuctSizerPage;
