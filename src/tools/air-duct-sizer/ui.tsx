import React, { useState, useCallback } from 'react';
import { AirDuctSizer, DuctShape, Units, DuctDimensions, DuctCalculationResult } from './logic';
import { DuctShapeSelector } from '../../components/air-duct-sizer/DuctShapeSelector';
import { UnitsSelector } from '../../components/air-duct-sizer/UnitsSelector';
import { FlowRateInput } from '../../components/air-duct-sizer/FlowRateInput';
import { DimensionsInput } from '../../components/air-duct-sizer/DimensionsInput';
import { LengthInput } from '../../components/air-duct-sizer/LengthInput';
import { ResultsDisplay } from '../../components/air-duct-sizer/ResultsDisplay';

interface DuctSizerState {
  shape: DuctShape;
  units: Units;
  flowRate: string;
  dimensions: DuctDimensions;
  length: string;
}

export const AirDuctSizerUI: React.FC = () => {
  const [state, setState] = useState<DuctSizerState>({
    shape: 'rectangular',
    units: 'imperial',
    flowRate: '',
    dimensions: {
      width: '',
      height: '',
      diameter: '',
    },
    length: '',
  });
  
  const [result, setResult] = useState<DuctCalculationResult | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Generate unique IDs for form controls
  const shapeId = React.useId();
  const unitsId = React.useId();
  const flowRateId = React.useId();
  const widthId = React.useId();
  const heightId = React.useId();
  const diameterId = React.useId();
  const lengthId = React.useId();

  const updateState = useCallback((updates: Partial<DuctSizerState>) => {
    setState(prev => ({
      ...prev,
      ...updates,
      dimensions: {
        ...prev.dimensions,
        ...(updates.dimensions || {}),
      },
    }));
  }, []);

  const handleErrorClear = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const validateInputs = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};
    const { flowRate, dimensions, shape, length } = state;
    
    // Validate flow rate
    const flowRateValue = parseFloat(flowRate);
    if (isNaN(flowRateValue) || flowRateValue <= 0) {
      newErrors.flowRate = 'Please enter a valid flow rate';
    }
    
    // Validate dimensions based on shape
    if (shape === 'rectangular') {
      const width = parseFloat(dimensions.width || '0');
      const height = parseFloat(dimensions.height || '0');
      
      if (isNaN(width) || width <= 0) {
        newErrors.width = 'Please enter a valid width';
      }
      
      if (isNaN(height) || height <= 0) {
        newErrors.height = 'Please enter a valid height';
      }
    } else {
      const diameter = parseFloat(dimensions.diameter || '0');
      if (isNaN(diameter) || diameter <= 0) {
        newErrors.diameter = 'Please enter a valid diameter';
      }
    }
    
    // Validate length
    const lengthValue = parseFloat(length);
    if (isNaN(lengthValue) || lengthValue <= 0) {
      newErrors.length = 'Please enter a valid length';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [state]);

  const handleCalculate = useCallback(() => {
    if (!validateInputs()) return;
    
    const { shape, units, flowRate, dimensions, length } = state;
    
    const calculator = new AirDuctSizer({
      shape,
      units,
      flowRate: parseFloat(flowRate),
      dimensions: {
        width: parseFloat(dimensions.width || '0'),
        height: parseFloat(dimensions.height || '0'),
        diameter: parseFloat(dimensions.diameter || '0'),
      },
      length: parseFloat(length),
    });
    
    const result = calculator.calculate();
    setResult(result);
  }, [state, validateInputs]);

  const handleClear = useCallback(() => {
    updateState({
      flowRate: '',
      dimensions: { width: '', height: '', diameter: '' },
      length: '',
    });
    setResult(null);
    setErrors({});
  }, [updateState]);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Air Duct Sizer</h1>
      
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DuctShapeSelector 
            shape={state.shape}
            setShape={(shape) => updateState({ shape })}
            shapeId={shapeId}
          />
          
          <UnitsSelector 
            units={state.units}
            setUnits={(units) => updateState({ units })}
            unitsId={unitsId}
          />
            <h4 className="font-semibold mb-2">SMACNA Guidelines</h4>
            <ul className="list-disc pl-5 space-y-1">
              <li key="velocity-guideline">Recommended velocity: 1,500-2,000 fpm (7.5-10 m/s) for main ducts</li>
              <li key="max-velocity-guideline">Maximum velocity: 2,500 fpm (12.5 m/s) for low-pressure systems</li>
              <li key="pressure-loss-guideline">Recommended pressure loss: 0.08 in wg/100ft (0.66 Pa/m) or less</li>
              <li key="max-pressure-loss-guideline">Maximum pressure loss: 0.15 in wg/100ft (1.23 Pa/m)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AirDuctSizerUI;
