{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "forceConsistentCasingInFileNames": true}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}